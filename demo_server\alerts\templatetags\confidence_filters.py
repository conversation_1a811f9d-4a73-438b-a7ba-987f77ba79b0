from django import template
from django.utils.safestring import mark_safe

register = template.Library()

@register.filter
def confidence_percentage(value):
    """
    Convert confidence value (0-1 range) to percentage display.
    
    Args:
        value: Confidence value between 0.0 and 1.0
        
    Returns:
        String formatted as percentage (e.g., "87%")
    """
    try:
        if value is None:
            return "0%"
        
        # Convert to float if needed
        confidence_float = float(value)
        
        # Convert from 0-1 range to 0-100 percentage
        percentage = confidence_float * 100.0
        
        # Format to 1 decimal place and add % symbol
        return f"{percentage:.1f}%"
    except (ValueError, TypeError):
        return "0%"

@register.filter
def confidence_badge_class(value):
    """
    Return Bootstrap badge class based on confidence level.
    
    Args:
        value: Confidence value between 0.0 and 1.0
        
    Returns:
        String with Bootstrap badge class
    """
    try:
        if value is None:
            return "bg-secondary"
        
        confidence_float = float(value)
        percentage = confidence_float * 100.0
        
        if percentage >= 80:
            return "bg-success"  # Green for high confidence
        elif percentage >= 60:
            return "bg-warning"  # Yellow for medium confidence
        else:
            return "bg-danger"   # Red for low confidence
    except (ValueError, TypeError):
        return "bg-secondary"

@register.filter
def confidence_text_class(value):
    """
    Return text color class based on confidence level.
    
    Args:
        value: Confidence value between 0.0 and 1.0
        
    Returns:
        String with text color class
    """
    try:
        if value is None:
            return "text-muted"
        
        confidence_float = float(value)
        percentage = confidence_float * 100.0
        
        if percentage >= 80:
            return "text-success"
        elif percentage >= 60:
            return "text-warning"
        else:
            return "text-danger"
    except (ValueError, TypeError):
        return "text-muted"
