import os
import numpy as np
import torch
from django.test import TestCase
from django.conf import settings
from PIL import Image
from .arcface_recognizer import ArcFaceRecognizer
from .facenet_recognizer import FaceNetRecognizer

class RecognizerTestCase(TestCase):
    def setUp(self):
        # Create a temporary directory for test images
        self.test_image_dir = os.path.join(settings.BASE_DIR, 'test_images')
        os.makedirs(self.test_image_dir, exist_ok=True)

        # Create a test image
        self.test_image_path = os.path.join(self.test_image_dir, 'test_face.jpg')
        self.create_test_image(self.test_image_path)

    def tearDown(self):
        # Clean up the test image directory
        for file in os.listdir(self.test_image_dir):
            os.remove(os.path.join(self.test_image_dir, file))
        os.rmdir(self.test_image_dir)

    def create_test_image(self, path):
        # Create a simple test image (you might want to use a real face image for better testing)
        img = Image.new('RGB', (112, 112), color = 'red')
        img.save(path)

    def test_arcface_recognizer_initialization(self):
        recognizer = ArcFaceRecognizer()
        self.assertIsNotNone(recognizer.detector)
        self.assertIsNotNone(recognizer.recognizer)

    def test_arcface_recognizer_prepare_facebank(self):
        recognizer = ArcFaceRecognizer()
        recognizer.prepare_facebank()
        self.assertIsNotNone(recognizer.embeddings)
        self.assertIsNotNone(recognizer.names)

    def test_arcface_recognizer_recognize(self):
        recognizer = ArcFaceRecognizer()
        image = cv2.imread(self.test_image_path)
        bboxes, names, scores = recognizer.recognize(image)
        self.assertIsInstance(bboxes, np.ndarray)
        self.assertIsInstance(names, list)
        self.assertIsInstance(scores, np.ndarray)

    def test_recognizer_initialization(self):
        recognizer = Recognizer()
        self.assertIsNotNone(recognizer.model)
        self.assertIsNotNone(recognizer.mtcnn)

    def test_recognizer_prepare_facebank(self):
        recognizer = Recognizer()
        recognizer.prepare_facebank()
        self.assertIsNotNone(recognizer.embeddings)
        self.assertIsNotNone(recognizer.names)

    def test_recognizer_recognize(self):
        recognizer = Recognizer()
        image = cv2.imread(self.test_image_path)
        bboxes, names, scores = recognizer.recognize(image)
        self.assertIsInstance(bboxes, np.ndarray)
        self.assertIsInstance(names, list)
        self.assertIsInstance(scores, torch.Tensor)

    def test_recognizer_add_face(self):
        recognizer = Recognizer()
        initial_embedding_count = recognizer.embeddings.shape[0] if recognizer.embeddings is not None else 0
        recognizer.add_face("Test Person", self.test_image_path)
        self.assertEqual(recognizer.embeddings.shape[0], initial_embedding_count + 1)
        self.assertIn("Test Person", recognizer.names)

    def test_recognizer_detect_faces(self):
        recognizer = Recognizer()
        image = cv2.imread(self.test_image_path)
        bboxes, faces = recognizer.detect_faces(image)
        self.assertIsInstance(bboxes, np.ndarray)
        self.assertIsInstance(faces, list)