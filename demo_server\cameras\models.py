from django.db import models
from django.conf import settings

# Create your models here.

class Camera(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='cameras')
    name = models.CharField(max_length=100)
    rtsp_url = models.CharField(max_length=255)
    is_streaming = models.BooleanField(default=False)  # New field to track streaming status
    last_seen = models.DateTimeField(null=True, blank=True)
    frame_drop_rate = models.FloatField(default=0.0)
    latency = models.FloatField(default=0.0)
    fps = models.FloatField(default=0.0)

    def is_healthy(self):
        # Basic health check: is the camera streaming?
        return self.is_streaming

    # Add any other fields or methods as needed