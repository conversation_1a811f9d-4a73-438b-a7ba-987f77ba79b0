Please answer the following questions for the following requirements.

Questions:

# Camera Management:
The "Camera locations are set by" statement is incomplete in the Access Control section
No specification for camera stream quality settings (resolution, fps)
No mention of camera health monitoring thresholds
# Integration Requirements:
No specification for door control system integration details (for buildings with automatic door opening)
No details about notification delivery preferences (email vs SMS vs push notifications)
No mention of third-party integration capabilities beyond payment systems
# Advanced Reporting (Premium Feature):
No specification of what types of advanced reports are available
No details about report scheduling capabilities
No mention of custom report builder features
# System Load Management:
No specific thresholds for system load monitoring
No details about load balancing configuration
No specification for maximum concurrent video streams per server
# Subscription Management:
No details about subscription cancellation process
No specification for handling overdue payments
No mention of subscription transfer between organizations
# Tenant Registration:
No specification for invitation link validity period
No details about bulk tenant import process
No mention of tenant verification process beyond email/SMS
# Building Management:
No specification for maximum buildings per site
No details about building hierarchy management
No mention of building access schedules


# Requirements of the project

## User Management
1. User can sign up
2. User can sign in
3. User can sign out
4. User can reset password
5. User can update profile
6. User can delete account
7. During registration verification email will be send. Also verification sms will be send. Both of them must be verified.
8. If user is inactive for 5 minutes, he is logged out.


## Camera Management
1. User can view camera list
2. User can add camera. It includes camera name, rtsp url, and camera type
3. User can update camera
4. User can delete camera
5. Camera list shows if each camera is streaming or not


## Alert Management
1. User can view alert person names as a list
2. The alert person list is paginated, showing a specified number of names per page
3. User can navigate between pages to view more alert person names
2. User can query alert photo by name
3. User can add alert person name
4. Uer can add multiple photos for the same name
5. User can delete alert user and his/her photo(s)
6. Uploaded photos will be saved in the django storage
7. Uploaded photos shall be larger or equal to defined size (width x height)
8. System will calculate the image vector by using ArcFace model and save it in the database
9. User can list and filter the generated alarms
10. Generted alarms has date, camera name, and person name and video snapshot of the related duration
11. User can view the alarm video snapshot when clicking on the alarm
12. Alarms will be deleted after 1 day (this is adjustable admin requirement)


## Non-functional requirements
1. aiortc is used for video streaming
2. django is used for user management, camera management, and alert management
3. opencv is used for image processing
4. pillow is used for image processing
5. django-storages is used for image storage
6. django-filter is used for filtering
7. django-rest-framework is used for api
8. django-cors-headers is used for cors
9. django-debug-toolbar is used for debugging
10. ArcFace model is used for face recognition
11. SCRFD model is used for face detection
12. ONNX is used for model inference
15. APIs are RESTful
16. APIs are versioned
17. APIs are paginated
18. APIs are filterable
19. APIs are sortable
20. APIs are searchable
21. APIs are secure
22. APIs are scalable
23. APIs are maintainable
24. APIs are testable
25. Unit test cases are written for all models, views, and serializers
26. Integration test cases are written for all views
27. End-to-end test cases are written for all views
28. Face recogintion and detection models can be changed dynamically 

----
New requirements

# Face detection requirements
1. If there is a detected face and it can't be found in the facebank then record it as a unkown new person, give it a unique name.
2. Unknown detected face size should be bigger than 64 pixels.
3. If detected face matches with an unknown person do not generate alarm but record it in the database. So in the database there will be alarms and repeated unalarmed events.
4. If there are multiple people in a frame record one video but link to all the people in the frame. There can be known and unkown people in the same frame.
5. Every detected face instance of a person will be recorded in the facebank so it will improve the facebank accuracy.


# Site manamgement requirements
1. Site admin manages the subscription levels.
2. Site admin enters flats, their numbers, tenants, their user names, first passwords, emails.
3. Site admin creates the main facebank data for each resident.
4. Residents can only see their flats photos and crud their guest photos. guest photos can be seen by site admin.Guest photos shall be recorded with name.
5. Optional. Allowed guest entry exit date/times can be entered for each guest.


## **🔹 Subscription Levels**
### **What subscription levels need to be supported?**
- Basic  
- Standard  
- Premium  

### **What features should be available at each subscription level?**
Following table is the initital configuration but it could be changable by the **system admin** via user interface.

| Feature | Basic | Standard | Premium |
|---------|-------|----------|---------|
| Max Flats | 1 | 5 | Unlimited |
| Max Tenants per Flat | 1 | 3 | 5+ |
| Max Cameras | 2 | 5 | 10+ |
| Alerts & Face Detection | ✅ | ✅ | ✅ |
| Historical Data Retention | 1 Month | 3 Months | Custom |
| Guest Management | ✅ | ✅ | ✅ |
| Guest Entry/Exit Tracking | ❌ | ✅ | ✅ |
| Advanced Reporting | ❌ | ❌ | ✅ |
| Priority Support | ❌ | ✅ | ✅ |
| Site (Organization) | 1 | 1 | 1 |

### **Should flats/tenants be limited based on subscription level?**
Yes. The **number of flats, tenants, and cameras** should be limited based on the selected subscription level.

---

## **🔹 Flat Management**
### **Should flats have additional attributes beyond just numbers?**
Yes, additional attributes can include:
- **Building/Block Number**
- **Floor Number**
- **Flat Size (sq ft/m²)**
- **Flat Type (Studio, 1BHK, 2BHK, etc.)**
- **Status (Occupied / Vacant)**
- **Notes (e.g., maintenance info, access restrictions)**


### **Do we need to track flat occupancy history?**
Yes. Keeping a **historical record** of occupancy is useful for audits, security, and tracking tenant changes.

### **Can a flat have multiple tenants simultaneously?**
Yes. A flat may have multiple tenants (e.g., families or shared apartments).

### **Should there be a way to mark flats as vacant/occupied?**
Yes. The system should support **manual changes** based on tenant presence.

---

## **🔹 Tenant Management**
### **What information needs to be stored for tenants beyond username, password, and email?**
- **Full Name**
- **Phone Number**
- **Flat Association**
- **Move-in Date**
- **Emergency Contact**
- **Access Permissions (e.g., can they add guests?)**
- **Profile Photo (optional)**

### **Should the system support tenant family members with separate accounts?**
Yes, tenants should be able to create accounts for **family members** (secondary tenants).

### **How should tenant turnover be handled (moving out/in)?**
- **When a tenant moves out:**
  - Their account should be **deactivated**.
  - Their personal data (photos, guest lists) should be **deleted or archived** after a set period.
  - Flat status should be updated to **vacant**.

- **When a new tenant moves in:**
  - Admin assigns them to a flat.
  - New tenant receives an **onboarding email**.
  - **Facebank is updated** with new residents.

### **Should there be a distinction between primary and secondary tenants?**
Yes. The **primary tenant** should have full control, while **secondary tenants** have limited permissions.

---

## **🔹 Guest Management**
### **How should guest photos be organized (per flat or per tenant)?**
- Guests should be **assigned per flat**.
- Guests should have an **link to a specific tenant**.

### **Should there be a limit on the number of guests per flat/tenant?**
Yes. Guest limits should be based on **subscription levels**.

### **For the optional entry/exit times:**
#### ✅ **Should recurring visits be supported?**
Yes. Admins or tenants should be able to set **daily, weekly, or monthly visit schedules**.

#### ✅ **Should there be a maximum duration for guest access?**
Yes, configurable by site admins (e.g., max 30 days).

#### ✅ **Should notifications be sent when guest access expires?**
Yes, notifications should be sent to:
- **Tenant** (reminding them to extend or revoke access).
- **Security Staff** (if needed).

---

## **🔹 Access Control**
### **What level of access should site admins have to tenant data?**
- **System Admins**: Can see all data.
- **Building Managers**: Can manage specific buildings/flats.
- **Security Staff**: Can view but not edit tenant/guest data.

### **Should there be different admin roles?**
Yes. Suggested roles:
0. **Super User** - Django admin. Manages all subscriptions and overall system
1. **System Admin** – Default subscriber role (can manage all buildings, billing, subscriptions).
2. **Site Admin** – Manages specific buildings and tenants.
3. **Security Staff** – Can only view cameras, guest lists, and access logs.

### **How should camera access be managed between flats?**
- Cameras only be managed by System Admins and Site Managers.
- Camera locations are set by 
- **Admins can access all cameras**.
- Tenants can only view cameras for **their own flat**.

---

## **🔹 Data Management**
### **How long should historical data be retained?**
- **Alerts:** 30 days (configurable per plan).
- **Facebank Data:** Kept **until tenant moves out**.
- **Guest Photos:** Deleted after guest is **removed or access expires**.
- **Tenant Data:** **Deleted after move-out** (GDPR compliant).

### **Should there be a way to transfer guest lists between tenants?**
- **No**, old records are soft deleted.

### **What should happen to guest data when a tenant moves out?**
- Guests **associated with the flat** should be **removed**.
- If the new tenant takes over the same flat, they cannot see old quests or records. Only System Admin can see the old records.


## Subscribers 
- Subscriber can create multiple organizations/sites.
- Subscriber is the default system admin.
- Site creation is restricted by subscription level
- users under sites cannot see each others data
- Tenants can register themselves via invitation link for a site.
- Tenants can register to multiple sites with the same email address, after login they can switch between sites.
- Subscriber can create/invite system admins.
- Subscriber and systems admins can see their payment history.
- Subscribers cannot see other subscribers data.

## Sites (Organizations)
- Sites are created by system admin
- Sites can include multiple buildings
- Sites can include multiple entry gates with camera
- Sites can include multiple cameras inside and outside of the perimeters/buildings.
- Sites have *Site Admins* to manage tenant/landlord(flat)/occupancy, camera configurations, etc.
- Sites have *Security Staff* to manage gates and watch camera streams, alarms, etc.


## Buildings
- Buildings have addresses and multiple flats with their levels
- Buildings may have separate entry/access camera to automatically open the door.
- Buildings may have corridor cameras

## Flats
- Flats may have multiple tenants or landlords (just a status, behaved as tenants)
- Flats have levels and door numbers

## Tenants (landlords for flats)
- Tenants can add quests for their flats
- Tenants can query entry/exit logs for themselves and their guests
- Tenants can delete their guests
- Tenants **cannot** delete entry/exit logs or camera alerts
- Tenants can add/update their photos but it should be approved first by security staff in order to be used by face detection

## Super User (Django Admin)
- Super User can see all subscriptions and their data
- Super user has a dashboard to track subscriptions and overall system usage data, such as cameras, sytem overload, gpu usage, etc.


# **🔹 User Management**
### **✅ Password Requirements/Complexity**
- Minimum **8-12 characters**.
- At least **1 uppercase, 1 lowercase, 1 number, and 1 special character**.
- **No reuse of last 5 passwords**.
- **Brute force protection** with rate limiting.

### **✅ Email Verification Process**
- When a user signs up:
  1. A **unique verification link** is sent via email (expires in 24 hours).
  2. A **one-time SMS code** (6-digit) is sent.
  3. User **must verify both** before first login.
  4. Resend limit: **3 attempts per 10 minutes**.

### **✅ Profile Fields That Can Be Updated**
- **Editable Fields**: Full Name, Phone Number, Profile Picture, Password.
- **Non-Editable Fields**: Email (requires re-verification if changed).

### **✅ Session Management & Timeout**
- **Inactive users** are logged out after **5 minutes**.
- **Session Expiry**: Default **24 hours**, can be adjusted.
- **Concurrent Logins**: Allowed but **restricted by IP-based rules**.

---

# **🔹 Camera Management**
### **✅ Supported Camera Types**
- **RTSP-supported IP cameras**.
- **Local USB cameras** (if running on-prem).
- **ONVIF cameras** (optional for future expansion).

### **✅ RTSP URL Validation**
- Validate RTSP URLs **before adding a camera**.
- **Automatic connection test** before saving.

### **✅ Maximum Allowed Cameras Per User**
- Defined by **subscription level** (e.g., Basic: 2, Standard: 5, Premium: 10).

### **✅ What "Streaming" Status Means**
- **Streaming = Active RTSP connection**.
- **Not Streaming = No active frames received** for **30+ seconds**.

---

# **🔹 Alert Management**
### **✅ Minimum/Maximum Photo Size**
- **Min Size**: 128x128 pixels.
- **Max Size**: 5MB per photo.

### **✅ Accepted Photo Formats**
- **JPEG, PNG, BMP** (no GIFs or animated formats).

### **✅ Maximum Photos Per Person**
- **Default: 5 photos per person** (configurable by admins).

### **✅ Alarm Pagination Size**
- **Default: 10 alerts per page** (adjustable by user).

### **✅ Alarm Filtering Criteria**
- **Date Range**
- **Camera Name**
- **Person Name**
- **Alarm Type (Known/Unknown)**
- **Face Confidence Score (e.g., Above 80%)**

### **✅ Video Snapshot Duration**
- **Default: 5 seconds before & after detection**.
- **Configurable range: 3-30 seconds**.

### **✅ How Alarms Are Generated**
- **Face match confidence** must be **above a threshold (e.g., 85%)**.
- **Unknown faces** are detected **if not in facebank**.

---

# **🔹 Face Detection Requirements**
### **✅ How Are Unique Names Generated for Unknown Faces?**
- `unknown_<timestamp>` (e.g., `unknown_20240201_143030`).
- If re-detected, it will **reuse the same unknown ID**.

### **✅ Confidence Threshold for Face Matching**
- **Default: 85% match confidence** (configurable).

### **✅ How "Improving Facebank Accuracy" Works**
- Each **new detected face instance** is added to the person’s **facebank vector**.
- More vectors **= better recognition over time**.

### **✅ Maximum Faces Per Frame**
- **Up to 10 faces per frame** (performance-dependent).
- **Batch processing** enabled for multi-person detections.

---

# **🔹 Site Management Requirements**
### **✅ Password Reset Process for Tenants**
- System generates a **temporary password** and sends it via **email & SMS**.
- User must **change the password upon first login**.

### **✅ Email Notification System**
- Notifications sent for:
  - **New tenants added**
  - **Guest approvals**
  - **Camera disconnections**
  - **Subscription renewals**

### **✅ Maximum Allowed Photos Per Resident**
- **Default: 10 per resident** (configurable by system admin).

### **✅ Guest Photo Approval Process**
- Guest photos **must be approved** by **Security Staff** before being used for face recognition.

---

# **🔹 Subscription System**
### **✅ Payment Processing System**
- **Stripe / PayPal / Local Payment Gateway**.

### **✅ Subscription Renewal Process**
- **Auto-renew enabled by default**.
- **Email reminders sent** before renewal.

### **✅ Upgrade/Downgrade Handling**
- **Upgrade**: Immediate effect, **prorated billing**.
- **Downgrade**: Takes effect **next billing cycle**.

### **✅ Trial Period / Free Tier**
- **7-day free trial** for new users.

---

# **🔹 Data Management**
### **✅ Backup/Restore Procedures**
- **Daily backups** to a secure **cloud storage**.
- Retention: **30 days**.
- **Admins can request restores** if needed.

### **✅ Data Export Formats**
- **CSV, JSON, XML** for reports.
- **MP4 for video clips**.

### **✅ Audit Logging Requirements**
- **All user actions** (e.g., login, camera edits, alarm deletions) are logged.
- **Admins can review logs**.

### **✅ Data Retention After Subscription Cancellation**
- **Data retained for 60 days** before deletion.

---

# **🔹 Access Control**
### **✅ Session Timeout Periods**
- **Inactive users logged out after 5 minutes**.
- **Configurable session expiry** (default: 24 hours).

### **✅ Failed Login Attempt Handling**
- **3 failed attempts → temporary 5-minute lockout**.
- **10 failed attempts → account disabled** (requires admin reset).

### **✅ IP-Based Access Restrictions**
- **Option to allow login from specific IP ranges** (for corporate accounts).

### **✅ Concurrent Login Handling**
- **Limit to 3 simultaneous logins per user** (configurable).

---

# **🔹 Technical Implementation**
### **✅ API Rate Limiting**
- **Default: 100 requests per minute** per user.

### **✅ Error Handling Standards**
- **Standardized error codes**.
- Example:
  - `400` – Bad Request (invalid input)
  - `401` – Unauthorized
  - `404` – Not Found
  - `500` – Internal Server Error

### **✅ Logging Requirements**
- Logs for:
  - **API requests** (user activity)
  - **System events** (camera errors, face recognition failures)
  - **Security alerts** (failed logins)

### **✅ Deployment Architecture**
- **Django Backend**
- **PostgreSQL for database**
- **Redis for caching**
- **Celery for async tasks**
- **Kubernetes (for scalability)**
- **S3 / Cloud storage for images & videos**

### **✅ Performance Requirements**
- Face recognition response **within 1 second**.
- **Camera stream latency** below **300ms**.

### **✅ Monitoring/Alerting Requirements**
- **Prometheus & Grafana** for monitoring.
- Alerts for:
  - **Camera disconnections**
  - **High system load**
  - **Suspicious login attempts**

Here are the missing specifications and clarifications for the identified gaps:

---

## **🔹 Camera Management**
### **✅ Camera Locations Setting**
- **Camera locations are set by the Site Admin.**
- Cameras can be assigned to **specific buildings, flats, gates, or corridors.**
- Each camera must have an **assigned location label** (e.g., "Lobby Entrance", "Garage", "Flat 101").

### **✅ Camera Stream Quality Settings**
- Supported **resolutions:** `480p, 720p, 1080p, 4K`
- Supported **frame rates:** `15fps, 30fps, 60fps`
- Default settings:
  - **Outdoor cameras:** `1080p, 30fps`
  - **Indoor cameras:** `720p, 30fps`
- Users can **adjust resolution & FPS** based on **network bandwidth and storage capacity**.

### **✅ Camera Health Monitoring**
- **Status Check Intervals:** Every `30 seconds`
- If a camera is **unresponsive for more than 5 minutes**, an **alert is generated**.
- Site Admins receive a **"Camera Disconnected" notification**.
- **Threshold for degraded streams**:
  - **Frame drop rate:** If more than `20%` frames are lost.
  - **Latency spike:** If stream delay exceeds `5 seconds`.
  - **Low FPS:** If FPS drops below `10`.

---

## **🔹 Integration Requirements**
### **✅ Door Control System Integration**
- Automatic doors **can be linked to cameras** for **facial recognition-based access.**
- Supported **door control protocols:** `ONVIF, Wiegand, MQTT`
- **Security Staff/Admins** can manually override access via the dashboard.

### **✅ Notification Delivery Preferences**
- Users can choose between:
  - **Email Notifications** ✅
  - **SMS Alerts** ✅
  - **Push Notifications (Mobile App)** ✅
  - **Webhook Integrations** for external systems ✅
- **Custom notification preferences** per user (e.g., "Only notify me of guest arrivals").

### **✅ Third-Party Integration Capabilities**
- Payment integration: `Stripe, PayPal`
- Additional integrations:
  - **Security Systems** (`ONVIF, Hikvision, Dahua`)
  - **Smart Home Systems** (`Home Assistant, Google Home, Alexa`)
  - **Building Management APIs** (for property management software)

---

## **🔹 Advanced Reporting (Premium Feature)**
### **✅ Available Advanced Reports**
1. **Entry/Exit Report** (Guest and tenant movement log)
2. **Face Recognition Accuracy Report** (False positives/negatives)
3. **Camera Performance Report** (Downtime, latency, FPS)
4. **Alert Trends** (Alarm frequency, known vs unknown faces)
5. **Subscription Usage Report** (Current vs allowed limits)

### **✅ Report Scheduling**
- Reports can be generated:
  - **Daily, Weekly, Monthly**
  - **Custom date ranges**
- Automated **email reports** (PDF, CSV, Excel).

### **✅ Custom Report Builder**
- Users can **define custom metrics** and generate tailored reports.
- Drag-and-drop interface for **choosing report fields**.

---

## **🔹 System Load Management**
### **✅ Load Monitoring Thresholds**
- **CPU Usage Threshold:** `80%`
- **Memory Usage Threshold:** `85%`
- **Disk Usage Threshold:** `90%`
- If thresholds are exceeded:
  - **Admins are notified**
  - **Auto-scaling is triggered** (if cloud deployment)

### **✅ Load Balancing Configuration**
- Uses **Nginx as reverse proxy** with **multiple Django instances**.
- Redis caching to **optimize API response times**.

### **✅ Maximum Concurrent Video Streams**
- **Per server:** `10 Full HD streams` OR `20 720p streams`
- **Per user:** Configurable based on **subscription plan**.

---

## **🔹 Subscription Management**
### **✅ Subscription Cancellation Process**
- Users can **cancel via the dashboard**.
- Data is **retained for 60 days** post-cancellation.
- Reactivation allowed **within retention period**.

### **✅ Handling Overdue Payments**
- **Grace Period:** `7 days`
- After **grace period**, system:
  - **Downgrades to Basic Plan** (limited access).
  - After `30 days` of non-payment, data is **permanently deleted**.

### **✅ Subscription Transfer Between Organizations**
- Subscribers can **transfer a site** to another subscriber.
- Requires **approval from both parties**.

---

## **🔹 Tenant Registration**
### **✅ Invitation Link Validity**
- **Expiration:** `7 days`
- **Resend Limit:** `3 times`

### **✅ Bulk Tenant Import**
- **Supported formats:** CSV, Excel
- Admin can bulk import **tenant details and pre-assign flats**.

### **✅ Tenant Verification Beyond Email/SMS**
- **Face verification (optional)**
- **Two-Factor Authentication (2FA)**
- **ID Upload for manual verification** (managed by Security Staff)

## **🔹 Building Management**
### **✅ Maximum Buildings Per Site**
- **Basic Plan:** `1 Building`
- **Standard Plan:** `5 Buildings`
- **Premium Plan:** `Unlimited`

### **✅ Building Hierarchy Management**
- **Site → Buildings → Flats**
- Each **building has multiple flats**.
- Each **flat is assigned to tenants**.

### **✅ Building Access Schedules**
- Entry permissions **can be scheduled** for:
  - **Specific time slots** (e.g., "Only between 9 AM - 5 PM")
  - **Days of the week** (e.g., "Only on Weekdays")
- Security Staff can **manually override schedules**.
