"""
Multiprocessing utilities for CUDA compatibility.

This module handles the setup of multiprocessing start method to 'spawn' 
which is required for CUDA compatibility in Django applications.
"""

import multiprocessing as mp
import logging

# Configure logging
logger = logging.getLogger(__name__)

def setup_multiprocessing():
    """
    Set multiprocessing start method to 'spawn' for CUDA compatibility.
    
    This function should be called at the beginning of Django entry points
    (manage.py, wsgi.py, asgi.py, settings.py) and any modules that use
    multiprocessing with CUDA.
    
    Returns:
        bool: True if successfully set, False if already set
    """
    try:
        mp.set_start_method('spawn', force=True)
        logger.info("Multiprocessing start method set to 'spawn' for CUDA compatibility")
        return True
    except RuntimeError as e:
        # Start method was already set, which is fine
        logger.info(f"Multiprocessing start method already set: {e}")
        return False

# Initialize on import to ensure it's set as early as possible
setup_multiprocessing()
