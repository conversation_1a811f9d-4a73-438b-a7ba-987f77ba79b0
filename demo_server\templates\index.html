{% extends 'base.html' %}

{% block content %}
<div class="container">
    <!-- Hero Section -->
    <div class="row justify-content-center mb-5">
        <div class="col-lg-10">
            <div class="text-center py-5">
                <div class="mb-4" data-aos="zoom-in">
                    <i class="bi bi-camera-fill text-primary" style="font-size: 4rem;"></i>
                </div>
                <h1 class="display-4 fw-bold text-primary mb-3" data-aos="fade-down">
                    Welcome to Face Recognition System 👋
                </h1>
                <p class="lead text-muted mb-4" data-aos="fade-up" data-aos-delay="100">
                    Advanced AI-powered face recognition and monitoring solution for enhanced security
                </p>
                
                {% if user.is_authenticated %}
                    <div class="alert alert-success border-0 shadow-sm" role="alert" data-aos="fade-in" data-aos-delay="200">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-person-check-fill me-3 fs-4"></i>
                            <div>
                                <h5 class="alert-heading mb-1">Welcome back, {{ user.username }}!</h5>
                                <p class="mb-0">You're successfully logged in and ready to manage your security system.</p>
                            </div>
                        </div>
                    </div>
                {% else %}
                    <div class="alert alert-info border-0 shadow-sm" role="alert" data-aos="fade-in" data-aos-delay="200">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-info-circle-fill me-3 fs-4"></i>
                            <div>
                                <h5 class="alert-heading mb-1">Get Started</h5>
                                <p class="mb-0">Please log in to access the face recognition system and manage your cameras.</p>
                            </div>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Feature Cards -->
    <div class="row g-4 mb-5">
        <div class="col-md-4" data-aos="fade-up" data-aos-delay="100">
            <div class="card h-100 border-0 shadow-sm hover-lift">
                <div class="card-body text-center p-4">
                    <div class="bg-primary bg-gradient rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <i class="bi bi-camera-video-fill text-white fs-4"></i>
                    </div>
                    <h5 class="card-title fw-bold">Camera Management</h5>
                    <p class="card-text text-muted">Monitor and manage multiple RTSP cameras with real-time streaming capabilities.</p>
                    {% if user.is_authenticated %}
                        <a href="{% url 'cameras:index' %}" class="btn btn-outline-primary">
                            <i class="bi bi-arrow-right me-1"></i>View Cameras
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-4" data-aos="fade-up" data-aos-delay="200">
            <div class="card h-100 border-0 shadow-sm hover-lift">
                <div class="card-body text-center p-4">
                    <div class="bg-success bg-gradient rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <i class="bi bi-person-badge-fill text-white fs-4"></i>
                    </div>
                    <h5 class="card-title fw-bold">Face Recognition</h5>
                    <p class="card-text text-muted">Advanced AI-powered face detection and recognition with high accuracy.</p>
                    {% if user.is_authenticated %}
                        <a href="{% url 'alerts:person_list' %}" class="btn btn-outline-success">
                            <i class="bi bi-arrow-right me-1"></i>Manage Persons
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-4" data-aos="fade-up" data-aos-delay="300">
            <div class="card h-100 border-0 shadow-sm hover-lift">
                <div class="card-body text-center p-4">
                    <div class="bg-warning bg-gradient rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <i class="bi bi-bell-fill text-white fs-4"></i>
                    </div>
                    <h5 class="card-title fw-bold">Alert System</h5>
                    <p class="card-text text-muted">Real-time alerts and notifications for detected faces and security events.</p>
                    {% if user.is_authenticated %}
                        <a href="{% url 'alerts:alert_list' %}" class="btn btn-outline-warning">
                            <i class="bi bi-arrow-right me-1"></i>View Alerts
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- User Profile Section -->
    {% if user.is_authenticated %}
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card border-0 shadow-sm" data-aos="fade-up" data-aos-delay="400">
                    <div class="card-header bg-gradient text-white border-0">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-person-circle me-2"></i>Profile Information
                        </h5>
                    </div>
                    <div class="card-body p-4">
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="bi bi-person-fill text-primary me-3 fs-5"></i>
                                    <div>
                                        <small class="text-muted">Username</small>
                                        <div class="fw-semibold">{{ user.username }}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="bi bi-envelope-fill text-primary me-3 fs-5"></i>
                                    <div>
                                        <small class="text-muted">Email</small>
                                        <div class="fw-semibold">{{ user.email|default:"Not provided" }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex flex-wrap gap-2 mt-4">
                            <a href="{% url 'users:profile' %}" class="btn btn-primary">
                                <i class="bi bi-pencil-square me-1"></i>Update Profile
                            </a>
                            <a href="{% url 'users:password_change' %}" class="btn btn-outline-secondary">
                                <i class="bi bi-key-fill me-1"></i>Change Password
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% else %}
        <!-- Login/Register Section -->
        <div class="row justify-content-center">
            <div class="col-lg-6">
                <div class="card border-0 shadow-sm" data-aos="fade-up" data-aos-delay="400">
                    <div class="card-body text-center p-5">
                        <i class="bi bi-shield-lock-fill text-primary mb-3" style="font-size: 3rem;"></i>
                        <h4 class="card-title fw-bold mb-3">Access Required</h4>
                        <p class="card-text text-muted mb-4">
                            Please log in to your account to access the face recognition system and manage your security cameras.
                        </p>
                        <div class="d-flex flex-column flex-sm-row gap-2 justify-content-center">
                            <a href="{% url 'users:login' %}" class="btn btn-primary btn-lg">
                                <i class="bi bi-box-arrow-in-right me-2"></i>Login
                            </a>
                            <a href="{% url 'users:register' %}" class="btn btn-outline-primary btn-lg">
                                <i class="bi bi-person-plus-fill me-2"></i>Register
                            </a>
                        </div>
                        <hr class="my-4">
                        <a href="{% url 'users:password_reset' %}" class="text-muted text-decoration-none">
                            <i class="bi bi-question-circle me-1"></i>Forgot your password?
                        </a>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}
