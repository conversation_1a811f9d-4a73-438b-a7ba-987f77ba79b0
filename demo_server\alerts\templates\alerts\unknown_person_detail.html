{% extends "base.html" %}
{% load static %}

{% block title %}{{ person.name }} - Details{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <h2>{{ person.name }}</h2>
            <p>First seen: {{ person.created_at|date:"d M Y, H:i" }}</p>
            <p>Last seen: {{ person.last_seen_date|date:"d M Y, H:i" }}</p>
            <p>First seen on camera: {{ person.first_seen_camera.name|default:"Unknown" }}</p>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    Rename Person
                </div>
                <div class="card-body">
                    <form method="post" action="{% url 'alerts:rename_unknown_person' person.id %}">
                        {% csrf_token %}
                        {{ form.non_field_errors }}
                        <div class="form-group">
                            {{ form.new_name.label_tag }}
                            {{ form.new_name }}
                            {{ form.new_name.errors }}
                            <small class="form-text text-muted">Enter the actual name of this person.</small>
                        </div>
                        <button type="submit" class="btn btn-success mt-3">Rename Person</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <h3 class="mt-4">Photos</h3>
    <div class="row">
        {% if page_obj %}
            {% for photo in page_obj %}
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <img src="{{ photo.photo.url }}" class="card-img-top" alt="{{ person.name }}">
                    <div class="card-body">
                        <p class="card-text">Captured: {{ photo.created_at|date:"d M Y, H:i" }}</p>
                        {% if photo.is_primary %}
                            <span class="badge bg-primary">Primary Photo</span>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="alert alert-info">
                    No photos found for this person.
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <nav aria-label="Photo navigation">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1">&laquo; first</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">previous</a>
                </li>
            {% endif %}

            {% for i in page_obj.paginator.page_range %}
                {% if page_obj.number == i %}
                    <li class="page-item active">
                        <span class="page-link">{{ i }} <span class="sr-only">(current)</span></span>
                    </li>
                {% elif i > page_obj.number|add:'-3' and i < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ i }}">{{ i }}</a>
                    </li>
                {% endif %}
            {% endfor %}

            {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">next</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">last &raquo;</a>
                </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
    
    <div class="mt-4">
        <a href="{% url 'alerts:unknown_persons_list' %}" class="btn btn-secondary">Back to Unknown Persons</a>
    </div>
</div>
{% endblock %}
