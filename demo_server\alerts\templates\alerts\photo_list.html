{% extends 'base.html' %}

{% block content %}
<h2>Person Photos</h2>
<div class="photo-grid">
    {% for photo in page_obj %}
    <div class="photo-item">
        <img src="{{ photo.photo.url }}" alt="{{ photo.person.name }}">
        <p>{{ photo.person.name }}</p>
    </div>
    {% endfor %}
</div>
<div class="pagination">
    <span class="step-links">
        {% if page_obj.has_previous %}
            <a href="?page=1">&laquo; first</a>
            <a href="?page={{ page_obj.previous_page_number }}">previous</a>
        {% endif %}
        <span class="current">
            Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}.
        </span>
        {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}">next</a>
            <a href="?page={{ page_obj.paginator.num_pages }}">last &raquo;</a>
        {% endif %}
    </span>
</div>
{% endblock %}
