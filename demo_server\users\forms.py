from django import forms
from django.contrib.auth.models import User

from alerts.forms import MultipleFileField, MultipleFileInput
from .models import UserProfile
from django.forms.widgets import DateTimeInput
from django.contrib.auth.forms import AuthenticationForm


class RegisterForm(forms.ModelForm):
    password = forms.CharField(widget=forms.PasswordInput)
    password_confirm = forms.CharField(widget=forms.PasswordInput, label='Confirm Password')
    
    class Meta:
        model = User
        fields = ['username', 'email', 'password', 'password_confirm']

    def clean(self):
        cleaned_data = super().clean()
        password = cleaned_data.get("password")
        password_confirm = cleaned_data.get("password_confirm")

        if password and password_confirm and password != password_confirm:
            raise forms.ValidationError("Passwords must match")
        return cleaned_data

# Yardımcı fonksiyonlar
def is_admin_user(user):
    """Super User veya System Admin mi kontrol et"""
    return user.is_superuser or (hasattr(user, 'userprofile') and user.userprofile.is_organizational_admin)


class UserProfileForm(forms.ModelForm):
    class Meta:
        model = UserProfile
        fields = []


class UserUpdateForm(forms.ModelForm):
    class Meta:
        model = User
        fields = ['username', 'email']