from celery import shared_task
from django.utils import timezone
from .models import Camera
from django.conf import settings
from django.core.mail import send_mail
from .models import Camera, Guest

@shared_task
def check_camera_health():
    cameras = Camera.objects.all()
    for camera in cameras:
        now = timezone.now()
        if camera.last_seen is not None:
            time_since_last_seen = now - camera.last_seen
            if time_since_last_seen.total_seconds() > 300:  # 5 minutes
                if camera.is_streaming:
                    camera.is_streaming = False
                    camera.save()
                    # Send "Camera Disconnected" notification to Site Admins
                    subject = f"Camera Disconnected: {camera.name}"
                    message = f"Camera {camera.name} has been disconnected for more than 5 minutes."
                    from_email = settings.DEFAULT_FROM_EMAIL
                    # TODO: Get Site Admins email list
                    recipient_list = [camera.user.email]  # For now, send to the camera owner
                    send_mail(subject, message, from_email, recipient_list)
                    print(f"Camera {camera.name} disconnected.")
            else:
                # Implement logic to check for frame drops, latency, and FPS
                if camera.frame_drop_rate > 0.2 or camera.latency > 5 or camera.fps < 10:
                    if camera.is_streaming:
                        camera.is_streaming = False
                        camera.save()
                        # Send "Camera Disconnected" notification to Site Admins
                        subject = f"Camera Degraded Stream: {camera.name}"
                        message = f"Camera {camera.name} has a degraded stream (Frame Drop Rate: {camera.frame_drop_rate}, Latency: {camera.latency}, FPS: {camera.fps})."
                        from_email = settings.DEFAULT_FROM_EMAIL
                        # TODO: Get Site Admins email list
                        recipient_list = [camera.user.email]  # For now, send to the camera owner
                        send_mail(subject, message, from_email, recipient_list)
                        print(f"Camera {camera.name} disconnected due to degraded stream.")
        else:
            # Camera has never been seen, mark as not streaming
            if camera.is_streaming:
                camera.is_streaming = False
                camera.save()
                print(f"Camera {camera.name} disconnected (never seen).")

@shared_task
def check_guest_access_expiration():
    now = timezone.now()
    guests = Guest.objects.all()
    for guest in guests:
        if guest.exit_date_time is None and guest.access_duration is not None:
            if guest.entry_date_time is not None:
                expiration_time = guest.entry_date_time + guest.access_duration
                if now > expiration_time:
                    # Send "Guest Access Expired" notification to Tenant and Security Staff
                    subject = f"Guest Access Expired: {guest.name}"
                    message = f"Guest {guest.name}'s access to flat {guest.flat.name} has expired."
                    from_email = settings.DEFAULT_FROM_EMAIL
                    # TODO: Get Tenant and Security Staff email list
                    recipient_list = [guest.flat.name]  # For now, send to the flat name
                    send_mail(subject, message, from_email, recipient_list)
                    print(f"Guest {guest.name}'s access expired.")
