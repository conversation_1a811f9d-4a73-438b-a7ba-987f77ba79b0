.alert-container {
        max-width: 800px;
        margin: 3rem auto;
        padding: 2rem;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        text-align: center;
    }

    .alert-search-container {
        max-width: 1600px;
        margin: 3rem auto;
        padding: 2rem;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        text-align: center;
    }

    .alert-listing-container {
        max-width: 1600px;
        margin: 3rem auto;
        padding: 2rem;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        text-align: center;
    }

    .alert-title {
        color: #2c3e50;
        font-size: 2rem;
        margin-bottom: 1.5rem;
    }

    .photo-upload-form {
        max-width: 600px;
        margin: 2rem auto;
        padding: 1.5rem;
        background-color: #f9f9f9;
        border: 1px solid #ddd;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .photo-upload-form button {
        background-color: #3498db;
        color: white;
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    .photo-upload-form button:hover {
        background-color: #2980b9;
    }

    .photo-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
        margin-top: 2rem;
    }

    .photo-item {
        border: 1px solid #ddd;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }

    .photo-item img {
        display: block;
        width: 100%;
        height: auto;
    }

    .paragraph {
        text-align: center;
        color: #7f8c8d;
        font-size: 1.2rem;
    }

    /* Dark Theme Styles for Alerts */
    [data-bs-theme="dark"] .alert-container {
        background-color: #2d3748 !important;
        border: 1px solid #4a5568 !important;
        color: #e2e8f0 !important;
    }

    [data-bs-theme="dark"] .alert-search-container {
        background-color: #2d3748 !important;
        border: 1px solid #4a5568 !important;
        color: #e2e8f0 !important;
    }

    [data-bs-theme="dark"] .alert-listing-container {
        background-color: #2d3748 !important;
        border: 1px solid #4a5568 !important;
        color: #e2e8f0 !important;
    }

    [data-bs-theme="dark"] .alert-title {
        color: #e2e8f0 !important;
    }

    /* Dark theme styling for photo upload form */
    [data-bs-theme="dark"] .photo-upload-form {
        background-color: #2d3748 !important;
        border: 1px solid #4a5568 !important;
        color: #e2e8f0 !important;
    }

    /* Dark theme form elements */
    [data-bs-theme="dark"] .photo-upload-form input,
    [data-bs-theme="dark"] .photo-upload-form textarea,
    [data-bs-theme="dark"] .photo-upload-form select {
        background-color: #1a202c !important;
        border: 1px solid #4a5568 !important;
        color: #e2e8f0 !important;
    }

    [data-bs-theme="dark"] .photo-upload-form input:focus,
    [data-bs-theme="dark"] .photo-upload-form textarea:focus,
    [data-bs-theme="dark"] .photo-upload-form select:focus {
        background-color: #2d3748 !important;
        border-color: #63b3ed !important;
        box-shadow: 0 0 0 0.2rem rgba(99, 179, 237, 0.25) !important;
    }

    [data-bs-theme="dark"] .photo-upload-form label {
        color: #e2e8f0 !important;
    }

    /* Dark theme for cards in alert forms */
    [data-bs-theme="dark"] .alert-container .card {
        background-color: #2d3748 !important;
        border: 1px solid #4a5568 !important;
        color: #e2e8f0 !important;
    }

    [data-bs-theme="dark"] .alert-container .card .form-control {
        background-color: #1a202c !important;
        border: 1px solid #4a5568 !important;
        color: #e2e8f0 !important;
    }

    [data-bs-theme="dark"] .alert-container .card .form-control:focus {
        background-color: #2d3748 !important;
        border-color: #63b3ed !important;
        box-shadow: 0 0 0 0.2rem rgba(99, 179, 237, 0.25) !important;
    }

    [data-bs-theme="dark"] .alert-container .card label {
        color: #e2e8f0 !important;
    }

    /* General dark theme for all form elements in alerts */
    [data-bs-theme="dark"] .alert-container input,
    [data-bs-theme="dark"] .alert-container textarea,
    [data-bs-theme="dark"] .alert-container select,
    [data-bs-theme="dark"] .alert-container .form-control,
    [data-bs-theme="dark"] .alert-container input[type="text"],
    [data-bs-theme="dark"] .alert-container input[type="email"],
    [data-bs-theme="dark"] .alert-container input[type="file"],
    [data-bs-theme="dark"] .alert-container input[type="number"] {
        background-color: #1a202c !important;
        border: 1px solid #4a5568 !important;
        color: #e2e8f0 !important;
    }

    [data-bs-theme="dark"] .alert-container input:focus,
    [data-bs-theme="dark"] .alert-container textarea:focus,
    [data-bs-theme="dark"] .alert-container select:focus,
    [data-bs-theme="dark"] .alert-container .form-control:focus,
    [data-bs-theme="dark"] .alert-container input[type="text"]:focus,
    [data-bs-theme="dark"] .alert-container input[type="email"]:focus,
    [data-bs-theme="dark"] .alert-container input[type="file"]:focus,
    [data-bs-theme="dark"] .alert-container input[type="number"]:focus {
        background-color: #2d3748 !important;
        border-color: #63b3ed !important;
        box-shadow: 0 0 0 0.2rem rgba(99, 179, 237, 0.25) !important;
    }

    [data-bs-theme="dark"] .alert-container label {
        color: #e2e8f0 !important;
    }

    [data-bs-theme="dark"] .photo-item {
        border: 1px solid #4a5568 !important;
        background-color: #1a202c !important;
    }

    [data-bs-theme="dark"] .paragraph {
        color: #a0aec0 !important;
    }

    /* Dark theme for photo gallery and cards */
    [data-bs-theme="dark"] .photo-card {
        background-color: #2d3748 !important;
        border: 1px solid #4a5568 !important;
        color: #e2e8f0 !important;
    }

    [data-bs-theme="dark"] .name-card {
        background-color: #2d3748 !important;
        border: 1px solid #4a5568 !important;
        color: #e2e8f0 !important;
        border-left: 5px solid #63b3ed !important;
    }

    [data-bs-theme="dark"] .person-title {
        color: #e2e8f0 !important;
    }

    /* Dark theme for Django form elements rendered with form.as_p */
    [data-bs-theme="dark"] .photo-upload-form p label {
        color: #e2e8f0 !important;
    }

    [data-bs-theme="dark"] .photo-upload-form p input[type="file"] {
        background-color: #1a202c !important;
        border: 1px solid #4a5568 !important;
        color: #e2e8f0 !important;
    }

    [data-bs-theme="dark"] .photo-upload-form p input[type="file"]:focus {
        background-color: #2d3748 !important;
        border-color: #63b3ed !important;
        box-shadow: 0 0 0 0.2rem rgba(99, 179, 237, 0.25) !important;
    }

    /* Dark theme for file input specifically */
    [data-bs-theme="dark"] input[type="file"] {
        background-color: #1a202c !important;
        border: 1px solid #4a5568 !important;
        color: #e2e8f0 !important;
    }

    [data-bs-theme="dark"] input[type="file"]::-webkit-file-upload-button {
        background-color: #4a5568 !important;
        border: 1px solid #63b3ed !important;
        color: #e2e8f0 !important;
        border-radius: 4px;
        padding: 0.375rem 0.75rem;
        margin-right: 0.5rem;
    }

    [data-bs-theme="dark"] input[type="file"]::-webkit-file-upload-button:hover {
        background-color: #63b3ed !important;
        color: #1a202c !important;
    }

    /* Comprehensive dark theme for ALL Django form elements */
    [data-bs-theme="dark"] .photo-upload-form p input,
    [data-bs-theme="dark"] .photo-upload-form p textarea,
    [data-bs-theme="dark"] .photo-upload-form p select,
    [data-bs-theme="dark"] .photo-upload-form input[type="text"],
    [data-bs-theme="dark"] .photo-upload-form input[type="email"],
    [data-bs-theme="dark"] .photo-upload-form input[type="password"],
    [data-bs-theme="dark"] .photo-upload-form input[type="number"],
    [data-bs-theme="dark"] .photo-upload-form input[type="url"],
    [data-bs-theme="dark"] .photo-upload-form input[type="tel"],
    [data-bs-theme="dark"] .photo-upload-form input[type="search"],
    [data-bs-theme="dark"] .photo-upload-form input[type="date"],
    [data-bs-theme="dark"] .photo-upload-form input[type="time"],
    [data-bs-theme="dark"] .photo-upload-form input[type="datetime-local"],
    [data-bs-theme="dark"] .photo-upload-form textarea,
    [data-bs-theme="dark"] .photo-upload-form select {
        background-color: #1a202c !important;
        border: 1px solid #4a5568 !important;
        color: #e2e8f0 !important;
    }

    [data-bs-theme="dark"] .photo-upload-form p input:focus,
    [data-bs-theme="dark"] .photo-upload-form p textarea:focus,
    [data-bs-theme="dark"] .photo-upload-form p select:focus,
    [data-bs-theme="dark"] .photo-upload-form input[type="text"]:focus,
    [data-bs-theme="dark"] .photo-upload-form input[type="email"]:focus,
    [data-bs-theme="dark"] .photo-upload-form input[type="password"]:focus,
    [data-bs-theme="dark"] .photo-upload-form input[type="number"]:focus,
    [data-bs-theme="dark"] .photo-upload-form input[type="url"]:focus,
    [data-bs-theme="dark"] .photo-upload-form input[type="tel"]:focus,
    [data-bs-theme="dark"] .photo-upload-form input[type="search"]:focus,
    [data-bs-theme="dark"] .photo-upload-form input[type="date"]:focus,
    [data-bs-theme="dark"] .photo-upload-form input[type="time"]:focus,
    [data-bs-theme="dark"] .photo-upload-form input[type="datetime-local"]:focus,
    [data-bs-theme="dark"] .photo-upload-form textarea:focus,
    [data-bs-theme="dark"] .photo-upload-form select:focus {
        background-color: #2d3748 !important;
        border-color: #63b3ed !important;
        box-shadow: 0 0 0 0.2rem rgba(99, 179, 237, 0.25) !important;
    }