{% extends 'base.html' %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0"><i class="fas fa-key me-2"></i>Change Password</h3>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="id_old_password" class="form-label">Current Password</label>
                            {{ form.old_password }}
                            {% if form.old_password.errors %}
                                <div class="text-danger small">{{ form.old_password.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="id_new_password1" class="form-label">New Password</label>
                            {{ form.new_password1 }}
                            {% if form.new_password1.errors %}
                                <div class="text-danger small">{{ form.new_password1.errors }}</div>
                            {% endif %}
                            {% if form.new_password1.help_text %}
                                <div class="small text-muted mt-1">{{ form.new_password1.help_text|safe }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="id_new_password2" class="form-label">Confirm New Password</label>
                            {{ form.new_password2 }}
                            {% if form.new_password2.errors %}
                                <div class="text-danger small">{{ form.new_password2.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'users:profile' %}" class="btn btn-secondary me-md-2">
                                <i class="fas fa-times me-1"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Change Password
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
