import cv2
import numpy as np
import math
from PIL import Image
from django.conf import settings
from django.core.files.base import ContentFile
from .models import Alarm
from recognition.facenet_recognizer import Face<PERSON><PERSON><PERSON>ognizer
from cameras.models import Camera
from django.utils import timezone

# Initialize the recognizer
facenet_recognizer = FaceNetRecognizer()

def calculate_image_vector(face_image):
    """Given a face image, calculate its vector using the recognizer."""
    # The image is already a numpy array (face_image)
    vector = facenet_recognizer.represent(img_path=face_image)
    return vector

def detect_faces(frame):
    """Detect faces in a given frame."""
    # The frame is a numpy array
    bboxes, landmarks = facenet_recognizer.detect(frame)
    return bboxes, landmarks

def distance_to_confidence_percentage(distance, threshold=0.63, steepness=8):
    """
    Convert Euclidean distance to confidence percentage using sigmoid function.

    Args:
        distance (float): Euclidean distance value from face recognition (e.g., 0.40)
        threshold (float): Recognition threshold value (default: 0.63)
        steepness (float): Controls the steepness of sigmoid curve (default: 8)

    Returns:
        float: Confidence percentage (0.0 to 100.0)

    Examples:
        >>> distance_to_confidence_percentage(0.20)  # Very good match
        ~95%
        >>> distance_to_confidence_percentage(0.40)  # Good match
        ~75%
        >>> distance_to_confidence_percentage(0.63)  # At threshold
        ~50%
        >>> distance_to_confidence_percentage(0.80)  # Poor match
        ~25%
    """
    if distance > threshold:
        # Beyond threshold - very low confidence
        # Use exponential decay for values beyond threshold
        excess = distance - threshold
        confidence = max(0.0, 50.0 * math.exp(-5 * excess))
        return confidence

    # Use inverse relationship: lower distance = higher confidence
    # Map distance linearly from threshold to 0, then apply sigmoid for smoothing
    normalized_distance = distance / threshold  # 0 to 1 range

    # Invert so that 0 distance = 100% confidence, threshold distance = 50% confidence
    inverted_score = 1.0 - normalized_distance

    # Apply sigmoid transformation for smooth curve
    # Center around 0.5 (which corresponds to threshold/2 distance)
    x = steepness * (inverted_score - 0.5)

    try:
        sigmoid_result = 1 / (1 + math.exp(-x))
        # Scale to give reasonable percentages: 50% at threshold, ~95% at very low distances
        confidence = 50.0 + (sigmoid_result - 0.5) * 90.0
        return max(0.0, min(100.0, confidence))
    except OverflowError:
        return 95.0 if distance < threshold/2 else 50.0

def confidence_percentage_to_distance(percentage, threshold=0.63, steepness=15):
    """
    Convert confidence percentage back to approximate distance (for backward compatibility).

    Args:
        percentage (float): Confidence percentage (0.0 to 100.0)
        threshold (float): Recognition threshold value (default: 0.63)
        steepness (float): Steepness parameter used in original conversion

    Returns:
        float: Approximate Euclidean distance value
    """
    if percentage <= 0:
        return threshold + 0.2  # Beyond threshold
    if percentage >= 100:
        return 0.0

    # Convert percentage back to sigmoid input
    confidence_ratio = percentage / 100.0

    if confidence_ratio >= 0.5:
        # Within threshold range - reverse sigmoid
        center_point = threshold / 2
        try:
            # Inverse sigmoid: x = -ln((1/y) - 1)
            x = -math.log((1 / confidence_ratio) - 1)
            distance = center_point - (x / steepness)
            return max(0.0, min(distance, threshold))
        except (ValueError, ZeroDivisionError):
            return center_point
    else:
        # Beyond threshold range - reverse exponential decay
        # 50 * exp(-10 * excess) = percentage
        # excess = -ln(percentage/50) / 10
        try:
            excess = -math.log(percentage / 50.0) / 10
            return threshold + excess
        except (ValueError, ZeroDivisionError):
            return threshold + 0.1

def recognize_face(image):
    """Recognize faces in a given image."""
    bboxes, names, scores = facenet_recognizer.recognize(image)
    return bboxes, names, scores

def add_face(name, filepath):
    """Add a face to the recognizer."""
    facenet_recognizer.add_face(name, filepath)