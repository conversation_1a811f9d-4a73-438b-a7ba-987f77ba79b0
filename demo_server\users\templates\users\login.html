{% extends 'base.html' %}

{% block content %}
<div class="container-fluid vh-100">
    <div class="row h-100">
        <!-- Left Side - Image/Branding -->
        <div class="col-lg-6 d-none d-lg-flex align-items-center justify-content-center bg-gradient">
            <div class="text-center text-white">
                <i class="bi bi-shield-lock-fill mb-4" style="font-size: 6rem;"></i>
                <h2 class="fw-bold mb-3">Secure Access</h2>
                <p class="lead mb-4">Advanced AI-powered face recognition system for enhanced security monitoring.</p>
                <div class="d-flex justify-content-center gap-4">
                    <div class="text-center">
                        <i class="bi bi-camera-video-fill mb-2" style="font-size: 2rem;"></i>
                        <div class="small">Live Monitoring</div>
                    </div>
                    <div class="text-center">
                        <i class="bi bi-cpu-fill mb-2" style="font-size: 2rem;"></i>
                        <div class="small">AI Recognition</div>
                    </div>
                    <div class="text-center">
                        <i class="bi bi-bell-fill mb-2" style="font-size: 2rem;"></i>
                        <div class="small">Real-time Alerts</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Side - Login Form -->
        <div class="col-lg-6 d-flex align-items-center justify-content-center">
            <div class="w-100" style="max-width: 400px;">
                <div class="card border-0 shadow-lg" data-aos="fade-left">
                    <div class="card-body p-5">
                        <!-- Logo/Title -->
                        <div class="text-center mb-4">
                            <i class="bi bi-person-circle text-primary mb-3" style="font-size: 4rem;"></i>
                            <h3 class="fw-bold text-primary mb-2">Welcome Back</h3>
                            <p class="text-muted">Sign in to access your security dashboard</p>
                        </div>

                        <!-- Login Form -->
                        <form method="post" class="needs-validation" novalidate>
                            {% csrf_token %}
                            
                            <!-- Username Field -->
                            <div class="mb-3">
                                <label for="{{ form.username.id_for_label }}" class="form-label fw-semibold">
                                    <i class="bi bi-person-fill text-primary me-2"></i>Username
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light border-end-0">
                                        <i class="bi bi-person"></i>
                                    </span>
                                    <input type="text" 
                                           name="{{ form.username.name }}" 
                                           id="{{ form.username.id_for_label }}"
                                           class="form-control border-start-0"
                                           placeholder="Enter your username"
                                           {% if form.username.value %}value="{{ form.username.value }}"{% endif %}
                                           required>
                                </div>
                                {% if form.username.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.username.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Password Field -->
                            <div class="mb-4">
                                <label for="{{ form.password.id_for_label }}" class="form-label fw-semibold">
                                    <i class="bi bi-lock-fill text-primary me-2"></i>Password
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light border-end-0">
                                        <i class="bi bi-lock"></i>
                                    </span>
                                    <input type="password" 
                                           name="{{ form.password.name }}" 
                                           id="{{ form.password.id_for_label }}"
                                           class="form-control border-start-0"
                                           placeholder="Enter your password"
                                           required>
                                    <button class="btn btn-outline-secondary border-start-0" type="button" id="togglePassword">
                                        <i class="bi bi-eye" id="toggleIcon"></i>
                                    </button>
                                </div>
                                {% if form.password.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.password.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Remember Me -->
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="rememberMe">
                                    <label class="form-check-label text-muted" for="rememberMe">
                                        Remember me
                                    </label>
                                </div>
                                <a href="{% url 'users:password_reset' %}" class="text-decoration-none small">
                                    Forgot password?
                                </a>
                            </div>

                            <!-- Login Button -->
                            <button type="submit" class="btn btn-primary btn-lg w-100 mb-3">
                                <i class="bi bi-box-arrow-in-right me-2"></i>Sign In
                            </button>

                            <!-- Social Login -->
                            <div class="text-center mb-3">
                                <span class="text-muted small">or continue with</span>
                            </div>
                            <a href="{% url 'social:begin' 'google-oauth2' %}" class="btn btn-outline-danger btn-lg w-100">
                                <i class="bi bi-google me-2"></i>Sign in with Google
                            </a>
                        </form>

                        <!-- Sign Up Link -->
                        <div class="text-center mt-4 pt-3 border-top">
                            <p class="text-muted mb-0">
                                Don't have an account? 
                                <a href="{% url 'users:register' %}" class="text-decoration-none fw-semibold">
                                    Create one here
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle password visibility
    const togglePassword = document.getElementById('togglePassword');
    const passwordField = document.getElementById('{{ form.password.id_for_label }}');
    const toggleIcon = document.getElementById('toggleIcon');

    if (togglePassword && passwordField && toggleIcon) {
        togglePassword.addEventListener('click', function() {
            const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordField.setAttribute('type', type);
            
            if (type === 'password') {
                toggleIcon.className = 'bi bi-eye';
            } else {
                toggleIcon.className = 'bi bi-eye-slash';
            }
        });
    }

    // Form validation
    const forms = document.querySelectorAll('.needs-validation');
    Array.from(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
});
</script>
{% endblock %}