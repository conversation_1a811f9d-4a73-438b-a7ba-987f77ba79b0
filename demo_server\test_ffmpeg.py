import os
import django
import time
import logging
import subprocess

def setup_django():
    """Initializes the Django environment."""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'web_server.settings')
    try:
        django.setup()
        logging.info("Django environment set up successfully.")
    except Exception as e:
        logging.error(f"Error setting up Django: {e}")
        exit(1)

def main():
    """Main function to run the test."""
    # Configure logging to show only WARNING level messages and above
    logging.basicConfig(level=logging.WARNING,
                        format='%(asctime)s - %(levelname)s - %(message)s')

    # --- IMPORTANT ---
    # Initialize Django BEFORE importing any of your project's modules
    setup_django()

    # Now it's safe to import your camera manager
    from videostream.camera_manager import camera_manager
    
    # --- Configuration ---
    image_file_path = '/home/<USER>/VisualStudioCodeProjects/face_recognition/demo_server/asker.jpg'
    number_of_test_streams = 2
    base_port = 8030
    ffmpeg_processes = []

    try:
        # --- Start FFmpeg Streams and Camera Instances ---
        for i in range(number_of_test_streams):
            port = base_port + i
            stream_url = f'http://127.0.0.1:{port}/feed.mjpeg'
            camera_id = i + 1

            logging.warning(f"Starting ffmpeg stream on port {port} for Camera {camera_id}...")
            
            command = [
                'ffmpeg', '-re', '-loop', '1', '-framerate', '10', '-i', image_file_path,
                '-f', 'mjpeg', '-listen', '1', stream_url
            ]
            
            # Start ffmpeg as a background process and hide its output
            proc = subprocess.Popen(command, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            ffmpeg_processes.append(proc)
            
            # Give ffmpeg a moment to start up before connecting
            time.sleep(2)

            # Start the camera stream connecting to the new unique URL
            class MockCamera:
                id = camera_id
            camera_manager.start_stream(camera_id, stream_url, MockCamera())
            logging.warning(f"Camera {camera_id} started successfully on {stream_url}")

        
        while True:
            time.sleep(10)

    except KeyboardInterrupt:
        print("\nKeyboard interrupt received.")
    
    finally:
        print("Stopping all streams and cleaning up ffmpeg processes...")
        camera_manager.stop_all_streams()
        for proc in ffmpeg_processes:
            proc.terminate() # or proc.kill()
        print("All processes stopped.")

if __name__ == '__main__':
    main()
