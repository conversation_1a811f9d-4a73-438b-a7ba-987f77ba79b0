/* Global Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.bg-light {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
}

/* Theme Styles */
[data-bs-theme="dark"] {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%) !important;
    color: #e9ecef !important;
}

[data-bs-theme="dark"] .bg-light {
    background: linear-gradient(135deg, #343a40 0%, #495057 100%) !important;
}

[data-bs-theme="dark"] .card {
    background-color: #2d3748 !important;
    border-color: #4a5568 !important;
    color: #e2e8f0 !important;
}

[data-bs-theme="dark"] .text-muted {
    color: #a0aec0 !important;
}

[data-bs-theme="dark"] .text-primary {
    color: #63b3ed !important;
}

[data-bs-theme="dark"] .border {
    border-color: #4a5568 !important;
}

/* Navbar Styles */
.navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
    padding: 1rem 0;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.4rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: 8px;
    margin: 0 2px;
    padding: 8px 12px !important;
    font-size: 1rem;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255,255,255,0.1);
    transform: translateY(-1px);
}

/* Theme Toggle Button */
#theme-toggle {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    border: 2px solid rgba(255,255,255,0.3);
}

#theme-toggle:hover {
    transform: scale(1.1);
    background-color: rgba(255,255,255,0.2) !important;
    border-color: rgba(255,255,255,0.5);
}

#theme-icon {
    transition: transform 0.3s ease;
}

#theme-toggle:hover #theme-icon {
    transform: rotate(180deg);
}

.dropdown-menu {
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    border-radius: 12px;
    padding: 8px;
}

.dropdown-item {
    border-radius: 8px;
    transition: all 0.2s ease;
    font-weight: 500;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

/* Card Enhancements */
.card {
    border-radius: 16px;
    transition: all 0.3s ease;
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(10px);
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.1) !important;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-radius: 16px 16px 0 0 !important;
}

/* Button Enhancements */
.btn {
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    padding: 10px 20px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.btn-outline-primary {
    border: 2px solid #667eea;
    color: #667eea;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: transparent;
}

/* Table Enhancements */
.table {
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.05);
}

.table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 600;
    padding: 15px;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
}

/* Form Enhancements */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    padding: 12px 15px;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Alert Enhancements */
.alert {
    border-radius: 12px;
    border: none;
    font-weight: 500;
}

/* Toast Enhancements */
.toast {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.toast-header {
    border-radius: 12px 12px 0 0;
    font-weight: 600;
}

/* Animation Classes */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.6s ease forwards;
}

/* Gradient Background */
.bg-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

/* Footer Styles */
footer {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
    margin-top: auto;
}

/* Main Content */
main {
    flex: 1;
    padding-top: 2rem;
    padding-bottom: 2rem;
}

/* Responsive Utilities */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .card-body {
        padding: 1.5rem !important;
    }
}

/* Loading Animation */
.loading {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 2s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Status Badges */
.badge {
    border-radius: 8px;
    font-weight: 500;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Theme Transition Styles */
body {
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Dark Theme Styles */
[data-bs-theme="dark"] {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%) !important;
    color: #e9ecef !important;
}

[data-bs-theme="dark"] .bg-light {
    background: linear-gradient(135deg, #343a40 0%, #495057 100%) !important;
}

[data-bs-theme="dark"] .card {
    background-color: #2d3748 !important;
    border-color: #4a5568 !important;
    color: #e2e8f0 !important;
}

[data-bs-theme="dark"] .text-muted {
    color: #a0aec0 !important;
}

[data-bs-theme="dark"] .text-primary {
    color: #63b3ed !important;
}

[data-bs-theme="dark"] .border {
    border-color: #4a5568 !important;
}

/* Theme Toggle Button */
#theme-toggle {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

#theme-toggle:hover {
    transform: scale(1.1);
    background-color: rgba(255,255,255,0.2) !important;
}

#theme-icon {
    transition: transform 0.3s ease;
}

#theme-toggle:hover #theme-icon {
    transform: rotate(180deg);
}

/* Theme Transition Styles */
body {
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Dark Theme Styles */
[data-bs-theme="dark"] {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%) !important;
    color: #e9ecef !important;
}

[data-bs-theme="dark"] .bg-light {
    background: linear-gradient(135deg, #343a40 0%, #495057 100%) !important;
}

[data-bs-theme="dark"] .card {
    background-color: #2d3748 !important;
    border-color: #4a5568 !important;
    color: #e2e8f0 !important;
}

[data-bs-theme="dark"] .text-muted {
    color: #a0aec0 !important;
}

[data-bs-theme="dark"] .text-primary {
    color: #63b3ed !important;
}

[data-bs-theme="dark"] .border {
    border-color: #4a5568 !important;
}

/* Theme Toggle Button */
#theme-toggle {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

#theme-toggle:hover {
    transform: scale(1.1);
    background-color: rgba(255,255,255,0.2) !important;
}

.custom-navbar .dropdown-item:hover,
.custom-navbar .dropdown-item:focus {
    background-color: #d2d6db;
    color: #ffffff;
}

.custom-navbar .nav-item.logout{
    background: #333;
    border-radius: 10px;
    margin-left: 10px;
}

.logout button:hover {
    background-color: #cf2424;
}
.custom-navbar .btn-link {
    color: #ffffff;
    padding: 0.5rem 1rem;
    text-decoration: none;
}

.custom-navbar .btn-link:hover,
.custom-navbar .btn-link:focus {
    color: #cccccc;
    text-decoration: none;
}
.custom-navbar .nav-item {
    margin: 0px 9px; /* Menü elemanları arasında boşluk bırakır */
}

.message {
    background-color: #d40d0d;
    color: #fff;
    padding: 14px 24px;
    border-radius: 4px;
    box-shadow: 0 3px 5px -1px rgba(0,0,0,.2), 0 6px 10px 0 rgba(0,0,0,.14), 0 1px 18px 0 rgba(0,0,0,.12);
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-width: 250px;
    max-width: 500px;
    opacity: 0;
    transform: translateY(50px);
    transition: opacity 0.3s, transform 0.3s;
}

.message.show {
    opacity: 1;
    transform: translateY(0);
}
.message.hide {
    opacity: 0;
}
.message-content {
    flex-grow: 1;
}

.message-close {
    background: none;
    border: none;
    color: #fff;
    cursor: pointer;
    font-size: 18px;
    margin-left: 10px;
}

.message.success { background-color: #43a047; }
.message.error { background-color: #d32f2f; }
.message.warning { background-color: #ffa000; }
.message.info { background-color: #1976d2; }
