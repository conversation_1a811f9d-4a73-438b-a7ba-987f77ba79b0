from django.http import JsonResponse
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from .models import Camera
from .forms import CameraForm

@login_required
def index(request):
    cameras = Camera.objects.filter(user=request.user)
    return render(request, 'cameras/index.html', {'cameras': cameras})

@login_required
def add_camera(request):
    if request.method == 'POST':
        form = CameraForm(request.POST)
        if form.is_valid():
            camera = form.save(commit=False)
            camera.user = request.user
            camera.save()
            return redirect('cameras:index')
    else:
        form = CameraForm()
    return render(request, 'cameras/add_camera.html', {'form': form})

@login_required
def update_camera(request, camera_id):
    camera = get_object_or_404(Camera, id=camera_id, user=request.user)
    if request.method == 'POST':
        form = CameraForm(request.POST, instance=camera)
        if form.is_valid():
            form.save()
            return redirect('cameras:index')
    else:
        form = CameraForm(instance=camera)
    return render(request, 'cameras/update_camera.html', {'form': form})

@login_required
def delete_camera(request, camera_id):
    camera = get_object_or_404(Camera, id=camera_id, user=request.user)
    if request.method == 'POST':
        camera.delete()
        return redirect('cameras:index')
    return render(request, 'cameras/confirm_delete.html', {'camera': camera})

@login_required
def camera_detail(request, camera_id):
    camera = get_object_or_404(Camera, id=camera_id, user=request.user)
    return render(request, 'cameras/camera_detail.html', {'camera': camera})

@login_required
def check_camera_status(request):
    """Return the streaming status of all cameras for this user"""
    cameras = Camera.objects.filter(user=request.user)
    statuses = {str(camera.id): camera.is_streaming for camera in cameras}
    return JsonResponse({'statuses': statuses})
