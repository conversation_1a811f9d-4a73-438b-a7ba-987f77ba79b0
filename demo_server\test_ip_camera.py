import os
import django
import time
import logging

def setup_django():
    """Initializes the Django environment."""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'web_server.settings')
    try:
        django.setup()
        logging.info("Django environment set up successfully.")
    except Exception as e:
        logging.error(f"Error setting up Django: {e}")
        exit(1)

def main():
    """Main function to run the test."""
    # Configure logging to show only WARNING level messages and above
    logging.basicConfig(level=logging.WARNING,
                        format='%(asctime)s - %(levelname)s - %(message)s')

    # --- IMPORTANT ---
    # Initialize Django BEFORE importing any of your project's modules
    setup_django()

    # Now it's safe to import your camera manager
    from videostream.camera_manager import camera_manager
    
    # --- Configuration ---
    # Direct IP camera stream URL
    ip_camera_url = 'http://************:5080/img_stream.jpeg'
    number_of_cameras = 1

    try:
        # --- Start IP Camera Instances ---
        for i in range(number_of_cameras):
            camera_id = i + 1

            # Start the camera stream connecting directly to the IP camera
            class MockCamera:
                id = camera_id
            camera_manager.start_stream(camera_id, ip_camera_url, MockCamera())
            logging.warning(f"IP Camera {camera_id} ready")
            
        while True:
            time.sleep(10)

    except KeyboardInterrupt:
        print("\nKeyboard interrupt received.")
    
    finally:
        print("Stopping all camera streams...")
        camera_manager.stop_all_streams()
        print("All camera streams stopped.")

if __name__ == '__main__':
    main()
