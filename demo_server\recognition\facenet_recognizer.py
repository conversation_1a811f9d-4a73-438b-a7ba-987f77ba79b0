from .base_recognizer import BaseRecognizer
from .facenet_model import Backbone, MobileFaceNet, l2_norm
import torch
import numpy as np
from torchvision import transforms as tv_transforms
from PIL import Image
from .facenet_mtcnn import MTCNN
import cv2
import os
import io
from django.conf import settings
from asyncio.log import logger
from alerts.models import AlertPhoto
import time
from videostream.logger import writer

class FaceNetRecognizer(BaseRecognizer):
    def __init__(self, mtcnn=None, model=None):
        """
        FaceNet tanıyıcı sınıfı - Dependency Injection yaklaşımı ile model bileşenlerini kabul eder
        Eğer model bileşenleri verilmezse kendisi yükler
        
        Args:
            mtcnn: Önceden yüklenmiş bir MTCNN nesnesi
            model: Önceden yüklenmiş bir FaceNet model nesnesi
        """
        logger.info("Initializing FaceNetRecognizer with dependency injection approach")
        self.conf = settings.RECOGNITION_CONFIG
        self.model_name = 'ir_se50.pth'
        
        # Model bileşenlerini yükle veya dışar<PERSON>dan alınan<PERSON> kullan
        self.mtcnn = mtcnn or self._load_mtcnn()
        self.model = model or self._load_model()
            
        # load facebank embeddings and names
        self._load_facebank()
        logger.info("FaceNetRecognizer initialization complete")

    def _load_mtcnn(self):
        """MTCNN yüz dedektörünü yükle"""
        logger.info("Loading FaceNet MTCNN detector")
        return MTCNN()
        
    def _load_model(self):
        """FaceNet modelini yükle"""
        logger.info("Loading FaceNet recognition model")
        
        if self.conf['use_mobilfacenet']:
            model = MobileFaceNet(self.conf['embedding_size']).to(self.conf['device'])
            logger.info('MobileFaceNet model generated')
        else:
            model = Backbone(self.conf['net_depth'], self.conf['drop_ratio'], self.conf['net_mode']).to(self.conf['device'])
            logger.info('{}_{} model generated'.format(self.conf['net_mode'], self.conf['net_depth']))

        model_path = os.path.join(os.getcwd(), self.conf['model_path'], f'model_{self.model_name}')
        model.load_state_dict(torch.load(model_path, map_location=torch.device(self.conf['device']), weights_only=True))
        model.eval()
        
        logger.info("FaceNetRecognizer model loaded")
        return model

    def _load_facebank(self):
        """Load facebank data"""
        if os.path.exists(os.path.join(self.conf['facebank_path'], 'facebank.pth')):
            self.embeddings = torch.load(os.path.join(self.conf['facebank_path'], 'facebank.pth'), map_location=self.conf['device'], weights_only=True)
            self.names = np.load(os.path.join(self.conf['facebank_path'], 'names.npy'), allow_pickle=True)
        else:
            self.prepare_facebank()
            
    def update_person_embeddings(self, person_name):
        """Update the embeddings only for a specific person"""
        if not person_name:
            logger.error("No person name provided for update_person_embeddings")
            return False

        # Find all indices where this person appears
        person_indices = []
        if self.names is not None:
            for i, name in enumerate(self.names):
                if name == person_name:
                    person_indices.append(i)

        # Determine the directory to scan based on whether it's an unknown person
        is_unknown = person_name.startswith("Unknown_")
        base_dir = self.conf['facebank_path']
        if is_unknown:
            person_dir = os.path.join(base_dir, 'unknowns', person_name)
        else:
            person_dir = os.path.join(base_dir, person_name)

        # If directory doesn't exist or can't be accessed, remove ALL instances of person from facebank
        if not os.path.exists(person_dir) or not os.access(person_dir, os.R_OK):
            if person_indices:
                # Remove ALL instances of the person from the facebank (reverse order to maintain indices)
                for idx in reversed(person_indices):
                    if idx == 0:
                        if len(self.names) > 1:
                            self.embeddings = self.embeddings[1:]
                            self.names = self.names[1:]
                        else:
                            self.embeddings = None
                            self.names = None
                            break
                    else:
                        self.embeddings = torch.cat([self.embeddings[:idx], self.embeddings[idx+1:]])
                        self.names = np.concatenate([self.names[:idx], self.names[idx+1:]])
                        
                logger.info(f"Removed {len(person_indices)} instances of {person_name} from FaceNet model")
            
            # Save the updated facebank
            torch.save(self.embeddings, os.path.join(self.conf['facebank_path'], 'facebank.pth'))
            np.save(os.path.join(self.conf['facebank_path'], 'names.npy'), self.names)
            return True

        # Gather all face embeddings for this person
        embs = []
        try:
            for file in os.scandir(person_dir):
                if not file.is_file():
                    continue
                try:
                    img = Image.open(file.path)
                    if img.size != (112, 112):
                        try:
                            img = self.mtcnn.align(img)
                        except Exception as e:
                            logger.error(f"Error aligning image {file.path}: {e}")
                            continue
                    
                    if img is None:
                        logger.error(f"Alignment failed for image {file.path}")
                        continue
                        
                    with torch.no_grad():
                        embs.append(self.model(self.conf['test_transform'](img).to(self.conf['device']).unsqueeze(0)))
                except Exception as e:
                    logger.error(f"Error processing image {file.path}: {e}")
                    continue
        except Exception as e:
            logger.error(f"Error scanning directory {person_dir}: {e}")
            return False

        # If no embeddings were found, remove ALL instances of person from facebank
        if len(embs) == 0:
            if person_indices:
                # Remove ALL instances of the person from the facebank (reverse order to maintain indices)
                for idx in reversed(person_indices):
                    if idx == 0:
                        if len(self.names) > 1:
                            self.embeddings = self.embeddings[1:]
                            self.names = self.names[1:]
                        else:
                            self.embeddings = None
                            self.names = None
                            break
                    else:
                        self.embeddings = torch.cat([self.embeddings[:idx], self.embeddings[idx+1:]])
                        self.names = np.concatenate([self.names[:idx], self.names[idx+1:]])
                        
                logger.info(f"Removed {len(person_indices)} instances of {person_name} from FaceNet model (no embeddings found)")
            
            # Save the updated facebank
            torch.save(self.embeddings, os.path.join(self.conf['facebank_path'], 'facebank.pth'))
            np.save(os.path.join(self.conf['facebank_path'], 'names.npy'), self.names)
            return True

        # Calculate the mean embedding
        embedding = torch.cat(embs).mean(0, keepdim=True)

        # Remove ALL existing instances and add one new instance
        if person_indices:
            # Remove ALL existing instances (reverse order to maintain indices)
            for idx in reversed(person_indices):
                if idx == 0:
                    if len(self.names) > 1:
                        self.embeddings = self.embeddings[1:]
                        self.names = self.names[1:]
                    else:
                        self.embeddings = None
                        self.names = None
                        break
                else:
                    self.embeddings = torch.cat([self.embeddings[:idx], self.embeddings[idx+1:]])
                    self.names = np.concatenate([self.names[:idx], self.names[idx+1:]])

        # Add the new single embedding for this person
        if self.embeddings is None:
            self.embeddings = embedding
            self.names = np.array([person_name])
        else:
            self.embeddings = torch.cat([self.embeddings, embedding])
            self.names = np.append(self.names, person_name)

        logger.info(f"Updated embeddings for {person_name} - consolidated {len(person_indices)} instances into 1")

        # Save the updated facebank
        torch.save(self.embeddings, os.path.join(self.conf['facebank_path'], 'facebank.pth'))
        np.save(os.path.join(self.conf['facebank_path'], 'names.npy'), self.names)
        return True

    def prepare_facebank(self):
        embeddings = []
        names = ['Unknown']

        # First scan regular face directories
        for path in os.scandir(self.conf['facebank_path']):
            if path.is_file():
                continue
            
            # Skip unknowns directory, we'll handle it separately
            if path.name == 'unknowns':
                continue
                
            embs = self._process_person_directory(path.path)
            
            if len(embs) == 0:
                print(f"No embeddings found for directory {path.path}")
                continue

            embedding = torch.cat(embs).mean(0, keepdim=True)
            embeddings.append(embedding)
            names.append(path.name)
            print(f'Added {path.name}')

        # Now scan unknowns directory if it exists
        unknowns_dir = os.path.join(self.conf['facebank_path'], 'unknowns')
        if os.path.exists(unknowns_dir) and os.path.isdir(unknowns_dir):
            for path in os.scandir(unknowns_dir):
                if not path.is_dir():
                    continue
                    
                embs = self._process_person_directory(path.path)
                
                if len(embs) == 0:
                    print(f"No embeddings found for unknown person {path.name}")
                    continue
                    
                embedding = torch.cat(embs).mean(0, keepdim=True)
                embeddings.append(embedding)
                names.append(path.name)
                print(f'Added unknown person {path.name}')

        if len(embeddings) == 0:
            # print("No embeddings were created")
            device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
            self.embeddings = None
            self.names = np.array([])
            torch.save(self.embeddings, os.path.join(self.conf['facebank_path'], 'facebank.pth'))
            np.save(os.path.join(self.conf['facebank_path'], 'names.npy'), self.names)
            return

        self.embeddings = torch.cat(embeddings)
        self.names = np.array(names)
        torch.save(self.embeddings, os.path.join(self.conf['facebank_path'], 'facebank.pth'))
        np.save(os.path.join(self.conf['facebank_path'], 'names.npy'), self.names)
        # print(f"Facebank prepared with {len(names)} persons")
        
    def _process_person_directory(self, directory_path):
        """Process a directory of face images and return embeddings"""
        embs = []
        for file in os.scandir(directory_path):
            if not file.is_file():
                continue
                
            try:
                img = Image.open(file.path)
            except Exception as e:
                print(f"Error opening image {file.path}: {e}")
                continue

            if img.size != (112, 112):
                try:
                    img = self.mtcnn.align(img)
                except Exception as e:
                    print(f"Error aligning image {file.path}: {e}")
                    continue

            if img is None:
                print(f"Alignment failed for image {file.path}")
                continue

            with torch.no_grad():
                try:
                    embs.append(self.model(self.conf['test_transform'](img).to(self.conf['device']).unsqueeze(0)))
                except Exception as e:
                    print(f"Error during model inference for image {file.path}: {e}")
                    continue
                    
        return embs

    def recognize(self, frame, threshold=1.11, camera_id=None):
        image = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
        start_time = time.time() 
        bboxes, faces = self.mtcnn.align_multi(image, self.conf['face_limit'], self.conf['min_face_size'])
        end_time = time.time()
        mtccn_delay = end_time - start_time
        writer.add_scalar(f'MTCNN_Delay/Camera_{camera_id}', mtccn_delay, start_time)
        if bboxes is not None and len(bboxes) > 0:
            # remove the last value of box and extend the box size 1 pixel in each side
            bboxes = bboxes[:, :-1]
            bboxes = bboxes.astype(int)
            bboxes = bboxes + [-1, -1, 1, 1]

            # Check if the embeddings are available
            if self.embeddings is None or len(self.embeddings) == 0:
                # Return all faces as unknown
                return (bboxes,
                       ["Unknown"] * len(bboxes),
                       [0.0] * len(bboxes))
                
            names = []
            infer_start_time = time.time()
            results, scores = self.infer_batch_process(faces, self.embeddings, threshold)
            infer_end_time = time.time()
            infer_delay = infer_end_time - infer_start_time
            writer.add_scalar(f'INFER_Delay/Camera_{camera_id}', infer_delay, start_time)
            for result in results:
                # Güvenli bir şekilde indeksleme yap - sınırları kontrol et
                result_int = result.item() if isinstance(result, torch.Tensor) else int(result)
                if result_int == -1:
                    names.append("Unknown")
                else:
                    # Sınırları kontrol et
                    name_index = result_int + 1
                    if 0 <= name_index < len(self.names):
                        names.append(self.names[name_index])
                    else:
                        # İndeks sınır dışıysa Unknown kullan
                        logger.warning(f"Index {name_index} out of bounds for self.names with length {len(self.names)}")
                        names.append("Unknown")
            return bboxes, names, scores
        else:
            return bboxes, [], []
        
    def infer_batch_process(self, faces, target_embs, threshold, tta=False):
        """
        Batch processing version of infer function for better performance
        Processes all faces at once instead of using for loop
        
        Args:
            faces: List of face images (PIL Images)
            target_embs: Target embeddings to compare against
            threshold: Distance threshold for recognition
            tta: Test Time Augmentation flag
            
        Returns:
            min_idx: Indices of matched persons (-1 for unknown)
            minimum: Minimum distances for each face
        """
        if len(faces) == 0:
            return torch.tensor([]), torch.tensor([])
        
        # Prepare batch tensor for all faces
        if tta:
            # For TTA, we need to process original and mirrored images - efficient approach
            # Transform and stack original images directly
            batch_tensor = torch.stack([self.conf['test_transform'](img) for img in faces]).to(self.conf['device'])
            # Transform and stack mirrored images directly
            batch_mirror_tensor = torch.stack([
                self.conf['test_transform'](tv_transforms.functional.hflip(img)) for img in faces
            ]).to(self.conf['device'])
            
            # Process both batches at once
            with torch.no_grad():
                embs = self.model(batch_tensor)
                embs_mirror = self.model(batch_mirror_tensor)
                
            # Combine original and mirrored embeddings with L2 normalization
            source_embs = l2_norm(embs + embs_mirror)
        else:
            # Standard batch processing without TTA - more efficient approach
            # Transform all images and stack directly into batch tensor
            batch_tensor = torch.stack([self.conf['test_transform'](img) for img in faces]).to(self.conf['device'])
            
            # Process entire batch at once
            with torch.no_grad():
                source_embs = self.model(batch_tensor)

        # Calculate distances between all source embeddings and target embeddings
        diff = source_embs.unsqueeze(-1) - target_embs.transpose(1, 0).unsqueeze(0)
        dist = torch.sum(torch.pow(diff, 2), dim=1)
        minimum, min_idx = torch.min(dist, dim=1)
        min_idx[minimum > threshold] = -1  # if no match, set idx to -1
        return min_idx, minimum

    def infer(self, faces, target_embs, threshold, tta=False):
        embs = []
        for img in faces:
            if tta:
                mirror = tv_transforms.functional.hflip(img)
                emb = self.model(self.conf['test_transform'](img).to(self.conf['device']).unsqueeze(0))
                emb_mirror = self.model(self.conf['test_transform'](mirror).to(self.conf['device']).unsqueeze(0))
                embs.append(l2_norm(emb + emb_mirror))
            else:
                embs.append(self.model(self.conf['test_transform'](img).to(self.conf['device']).unsqueeze(0)))
        source_embs = torch.cat(embs)

        diff = source_embs.unsqueeze(-1) - target_embs.transpose(1, 0).unsqueeze(0)
        dist = torch.sum(torch.pow(diff, 2), dim=1)
        minimum, min_idx = torch.min(dist, dim=1)
        min_idx[minimum > threshold] = -1  # if no match, set idx to -1
        return min_idx, minimum

    def add_face(self, name, filepath):
        try:
            img = Image.open(filepath)
            # Align face if needed
            if img.size != (112, 112):
                try:
                    img = self.mtcnn.align(img, self.conf['min_face_size'])
                except Exception as e:
                    logger.error(f"Error aligning face for {name}: {str(e)}")
                    return False
            
            if img is None:
                logger.error(f"Face alignment failed for {name}")
                return False
                
            # Generate embedding
            with torch.no_grad():
                emb = self.model(self.conf['test_transform'](img).to(self.conf['device']).unsqueeze(0))
            
            # Set device consistently
            device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
            emb = emb.to(device)
            
            # Update tensor and names
            if self.embeddings is None or len(self.embeddings) == 0:
                self.embeddings = emb
                self.names = np.array([name])
            else:
                # Ensure embeddings are on the same device as new emb
                self.embeddings = self.embeddings.to(device)
                self.embeddings = torch.cat([self.embeddings, emb])
                self.names = np.append(self.names, name)
            
            # Save updated facebank
            torch.save(self.embeddings, os.path.join(self.conf['facebank_path'], 'facebank.pth'))
            np.save(os.path.join(self.conf['facebank_path'], 'names.npy'), self.names)
            
            # Serialize tensor to binary format and save to AlertPhoto
            buffer = io.BytesIO()
            torch.save(emb, buffer)
            buffer.seek(0)
            
            try:
                photo_instance = AlertPhoto.objects.get(photo=filepath)
                photo_instance.image_vector_facenet = buffer.getvalue()
                photo_instance.save()
            except AlertPhoto.DoesNotExist:
                logger.info(f"No AlertPhoto found for {filepath}, continuing without saving vector")
            
            return True
        except Exception as e:
            logger.error(f"Error in add_face: {str(e)}")
            return False

    def detect_faces(self, frame):
        image = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
        bboxes, faces = self.mtcnn.align_multi(image, self.conf['face_limit'], self.conf['min_face_size'])
        return bboxes, faces

    def load_tensor(self, data):
        buffer = io.BytesIO(data)
        tensor = torch.load(buffer)
        return tensor