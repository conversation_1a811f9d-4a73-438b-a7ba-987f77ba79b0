from django.test import TestCase
from cameras.models import Camera
from django.utils import timezone
from django.contrib.auth.models import User
from cameras.models import Flat, Guest
from unittest.mock import patch
from cameras.tasks import check_guest_access_expiration

class CameraHealthTests(TestCase):
    def setUp(self):
        # Set up non-modified objects used by all test methods
        self.user = User.objects.create_user(username='testuser', password='testpassword')
        self.camera = Camera.objects.create(
            user=self.user,
            name="Test Camera",
            rtsp_url="rtsp://test"
        )

    def test_camera_is_healthy(self):
        # Test that a camera is considered healthy if it's streaming
        self.camera.is_streaming = True
        self.camera.last_seen = timezone.now()
        self.assertTrue(self.camera.is_healthy())

    def test_camera_is_unhealthy_if_not_streaming(self):
        # Test that a camera is considered unhealthy if it's not streaming
        self.camera.is_streaming = False
        self.camera.last_seen = None
        self.assertFalse(self.camera.is_healthy())

    def test_camera_health_monitoring(self):
        # This test will simulate camera health monitoring
        # In real implementation, this will check for frame drops, latency, etc.
        # For now, we will just check if the camera is streaming
        self.camera.is_streaming = False
        self.camera.last_seen = None
        self.assertFalse(self.camera.is_healthy())
        # Simulate camera starting to stream
        self.camera.is_streaming = True
        self.camera.last_seen = timezone.now()
        self.assertTrue(self.camera.is_healthy())

class GuestTrackingTests(TestCase):
    def setUp(self):
        # Set up non-modified objects used by all test methods
        self.user = User.objects.create_user(username='testuser', password='testpassword')
        self.flat = Flat.objects.create(name="Test Flat")
        self.camera = Camera.objects.create(
            user=self.user,
            name="Test Camera",
            rtsp_url="rtsp://test"
        )
        self.guest = Guest.objects.create(
            flat=self.flat,
            name="Test Guest",
            photo="guest_photos/test.jpg",
            recurring=True,
            recurring_days="Mon,Wed,Fri",
            access_duration=timezone.timedelta(days=1)
        )

    def test_recurring_visits_supported(self):
        # Test that recurring visits are supported
        self.assertTrue(self.guest.recurring)
        self.assertEqual(self.guest.access_duration, timezone.timedelta(days=1))

    def test_maximum_duration_for_guest_access_supported(self):
        # Test that a maximum duration for guest access is supported
        self.assertEqual(self.guest.access_duration, timezone.timedelta(days=1))

    def test_notifications_sent_when_guest_access_expires(self):
        # Test that notifications are sent when guest access expires
        with patch('django.core.mail.send_mail') as mock_send_mail:
            self.guest.entry_date_time = timezone.now() - timezone.timedelta(days=2)
            self.guest.save()
            from cameras.tasks import check_guest_access_expiration
            check_guest_access_expiration()
            mock_send_mail.assert_called_once()

    def test_create_camera(self):
        """Test creating a new camera."""
        self.client.login(username='testuser', password='password')
        response = self.client.post(reverse('cameras:add'), {
            'name': 'New Test Camera',
            'rtsp_url': 'rtsp://new_test_camera'
        })
        self.assertEqual(response.status_code, 302)  # Should redirect after successful creation
        self.assertTrue(Camera.objects.filter(name='New Test Camera').exists())

    def test_camera_list_view(self):
        """Test the camera list view."""
        self.client.login(username='testuser', password='password')
        response = self.client.get(reverse('cameras:list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.camera.name)

    def test_delete_camera(self):
        """Test deleting a camera."""
        self.client.login(username='testuser', password='password')
        response = self.client.post(reverse('cameras:delete', args=[self.camera.id]))
        self.assertEqual(response.status_code, 302)  # Should redirect after successful deletion
        self.assertFalse(Camera.objects.filter(id=self.camera.id).exists())
