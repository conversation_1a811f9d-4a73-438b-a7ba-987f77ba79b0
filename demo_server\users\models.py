from django.db import models
from django.contrib.auth.models import User

# Create your models here.

class UserProfile(models.Model):
    RECOGNIZER_CHOICES = [
        ('facenet', 'FaceNetRecognizer'),
    ]
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    preferred_recognizer = models.CharField(
        max_length=20,
        choices=RECOGNIZER_CHOICES,
        default='facenet',
    )
    is_organizational_admin = models.BooleanField(default=False)

    def __str__(self):
        return self.user.username

