{% extends 'base.html' %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <h2>{{ person.name }} - Entry/Exit Times</h2>
            <p class="text-muted">Showing data for {{ selected_date|date:"F d, Y" }}</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{% url 'alerts:person_list' %}" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Back to Persons
            </a>
        </div>
    </div>

    <!-- Date Selection -->
    <div class="card mb-4">
        <div class="card-body">
            <h5 class="card-title">Select Date</h5>
            <form method="get" class="row g-3">
                <div class="col-md-6">
                    <input type="date" 
                           name="date" 
                           value="{{ selected_date|date:'Y-m-d' }}" 
                           class="form-control"
                           onchange="this.form.submit()">
                </div>
                <div class="col-md-6">
                    <div class="btn-group" role="group">
                        <a href="?date={% now 'Y-m-d' %}" class="btn btn-outline-primary btn-sm">Today</a>
                        <a href="?date={% now 'Y-m-d'|add:'-1 day' %}" class="btn btn-outline-primary btn-sm">Yesterday</a>
                        <a href="?date={% now 'Y-m-d'|add:'-7 days' %}" class="btn btn-outline-primary btn-sm">Week Ago</a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Times Data -->
    {% if has_data %}
        <div class="row">
            {% for camera_name, data in camera_summary.items %}
            <div class="col-12 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-camera"></i> {{ camera_name }}
                            <span class="badge bg-secondary ms-2">{{ data.total_events }} events</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        {% if data.entry_exit_pairs %}
                            <!-- Entry/Exit Pairs -->
                            {% for pair in data.entry_exit_pairs %}
                            <div class="row mb-3 p-3 border rounded">
                                <div class="col-md-4">
                                    <h6 class="text-success">
                                        <i class="bi bi-box-arrow-in-right"></i> Entry
                                    </h6>
                                    {% if pair.entry %}
                                        <p class="h5 text-success">{{ pair.entry.date|time:"H:i:s" }}</p>
                                        <small class="text-muted">
                                            Confidence: {{ pair.entry.confidence|floatformat:2 }}
                                        </small>
                                    {% if pair.entry.person_photo %}
                                    <div class="mt-2">
                                        <a href="{{ pair.entry.person_photo.photo.url }}" target="_blank">
                                            <img src="{{ pair.entry.person_photo.photo.url }}" class="img-thumbnail" style="max-height: 80px;">
                                        </a>
                                    </div>
                                    {% elif pair.entry.video_snapshot %}
                                    <div class="mt-2">
                                        <a href="{{ pair.entry.video_snapshot.url }}" target="_blank">
                                            <img src="{{ pair.entry.video_snapshot.url }}" class="img-thumbnail" style="max-height: 80px;">
                                        </a>
                                    </div>
                                    {% endif %}
                                {% else %}
                                    <p class="text-muted">No entry recorded</p>
                                {% endif %}
                            </div>
                                <div class="col-md-4">
                                    <h6 class="text-danger">
                                        <i class="bi bi-box-arrow-right"></i> Exit
                                    </h6>
                                    {% if pair.exit %}
                                        <p class="h5 text-danger">{{ pair.exit.date|time:"H:i:s" }}</p>
                                        <small class="text-muted">
                                            Confidence: {{ pair.exit.confidence|floatformat:2 }}
                                        </small>
                                    {% if pair.exit.person_photo %}
                                    <div class="mt-2">
                                        <a href="{{ pair.exit.person_photo.photo.url }}" target="_blank">
                                            <img src="{{ pair.exit.person_photo.photo.url }}" class="img-thumbnail" style="max-height: 80px;">
                                        </a>
                                    </div>
                                    {% elif pair.exit.video_snapshot %}
                                        <div class="mt-2">
                                            <a href="{{ pair.exit.video_snapshot.url }}" target="_blank">
                                                <img src="{{ pair.exit.video_snapshot.url }}" class="img-thumbnail" style="max-height: 80px;">
                                            </a>
                                        </div>
                                    {% endif %}
                                {% else %}
                                    <p class="text-muted">Still inside</p>
                                {% endif %}
                            </div>
                                <div class="col-md-4">
                                    <h6 class="text-info">
                                        <i class="bi bi-clock"></i> Duration
                                    </h6>
                                    {% if pair.duration %}
                                        <p class="h5 text-info">
                                            {% if pair.hours %}{{ pair.hours }}h {% endif %}{{ pair.minutes }}m
                                        </p>
                                        <small class="text-muted">
                                            {{ pair.duration }}
                                        </small>
                                    {% else %}
                                        <p class="text-muted">-</p>
                                    {% endif %}
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted text-center">No entry/exit records for this camera</p>
                        {% endif %}

                        <div class="mt-3">
                            <button class="btn btn-sm btn-outline-info"
                                    type="button"
                                    data-bs-toggle="collapse"
                                    data-bs-target="#details-{{ forloop.counter }}"
                                    aria-expanded="false">
                                <i class="bi bi-list-ul"></i> View All Events ({{ data.total_events }})
                            </button>
                        </div>

                        <div class="collapse mt-3" id="details-{{ forloop.counter }}">
                            <div class="card card-body">
                                <h6>All Events:</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm table-striped">
                                        <thead>
                                            <tr>
                                                <th>Type</th>
                                                <th>Time</th>
                                                <th>Confidence</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for entry in data.entries %}
                                            <tr>
                                                <td><span class="badge bg-success">Entry</span></td>
                                                <td>{{ entry.timestamp|time:"H:i:s" }}</td>
                                                <td>
                                                    <span class="badge bg-{% if entry.confidence > 0.9 %}success{% elif entry.confidence > 0.7 %}warning{% else %}danger{% endif %}">
                                                        {{ entry.confidence|floatformat:2 }}
                                                    </span>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                            {% for exit in data.exits %}
                                            <tr>
                                                <td><span class="badge bg-danger">Exit</span></td>
                                                <td>{{ exit.timestamp|time:"H:i:s" }}</td>
                                                <td>
                                                    <span class="badge bg-{% if exit.confidence > 0.9 %}success{% elif exit.confidence > 0.7 %}warning{% else %}danger{% endif %}">
                                                        {{ exit.confidence|floatformat:2 }}
                                                    </span>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="alert alert-info text-center" role="alert">
            <i class="bi bi-info-circle"></i>
            <h4>No Records Found</h4>
            <p>No entry/exit records found for <strong>{{ person.name }}</strong> on {{ selected_date|date:"F d, Y" }}.</p>
            <hr>
            <p class="mb-0">Try selecting a different date or check if the person has been detected by any camera.</p>
        </div>
    {% endif %}
</div>

<style>
.badge {
    font-size: 0.75em;
}
.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.card-header {
    border-bottom: 1px solid #e9ecef;
}
</style>
{% endblock %}
