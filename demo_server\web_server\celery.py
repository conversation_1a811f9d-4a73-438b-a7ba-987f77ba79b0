import os
from celery import Celery

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'web_server.settings')

app = Celery('web_server',
             broker='redis://localhost:6379',  # Replace with your Redis broker URL
             backend='redis://localhost:6379',   # Replace with your Redis backend URL
             include=['cameras.tasks'])

# Optional configuration, see the application user guide.
app.conf.update(
    result_expires=3600,
)

if __name__ == '__main__':
    app.start()
