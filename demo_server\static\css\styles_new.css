/* Global Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.bg-light {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
}

/* Dark Theme Styles */
[data-bs-theme="dark"] {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%) !important;
    color: #e9ecef !important;
}

[data-bs-theme="dark"] .bg-light {
    background: linear-gradient(135deg, #343a40 0%, #495057 100%) !important;
}

[data-bs-theme="dark"] .card {
    background-color: #2d3748 !important;
    border-color: #4a5568 !important;
    color: #e2e8f0 !important;
}

[data-bs-theme="dark"] .text-muted {
    color: #a0aec0 !important;
}

[data-bs-theme="dark"] .text-primary {
    color: #63b3ed !important;
}

[data-bs-theme="dark"] .border {
    border-color: #4a5568 !important;
}

[data-bs-theme="dark"] .table {
    background-color: #2d3748 !important;
    color: #e2e8f0 !important;
}

[data-bs-theme="dark"] .table thead th {
    background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%) !important;
    color: #e2e8f0 !important;
}

[data-bs-theme="dark"] .form-control {
    background-color: #4a5568 !important;
    border-color: #718096 !important;
    color: #e2e8f0 !important;
}

[data-bs-theme="dark"] .form-control:focus {
    background-color: #4a5568 !important;
    border-color: #63b3ed !important;
    color: #e2e8f0 !important;
}

[data-bs-theme="dark"] .dropdown-menu {
    background-color: #2d3748 !important;
    border-color: #4a5568 !important;
}

[data-bs-theme="dark"] .dropdown-item {
    color: #e2e8f0 !important;
}

[data-bs-theme="dark"] .dropdown-item:hover {
    background-color: #4a5568 !important;
    color: #ffffff !important;
}

/* Navbar Styles */
.navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
    padding: 1rem 0;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.4rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: 8px;
    margin: 0 2px;
    padding: 8px 12px !important;
    font-size: 1rem;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255,255,255,0.1);
    transform: translateY(-1px);
}

/* Theme Toggle Button */
#theme-toggle {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    border: 2px solid rgba(255,255,255,0.3);
}

#theme-toggle:hover {
    transform: scale(1.1);
    background-color: rgba(255,255,255,0.2) !important;
    border-color: rgba(255,255,255,0.5);
}

#theme-icon {
    transition: transform 0.3s ease;
}

#theme-toggle:hover #theme-icon {
    transform: rotate(180deg);
}

.dropdown-menu {
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    border-radius: 12px;
    padding: 8px;
}

.dropdown-item {
    border-radius: 8px;
    transition: all 0.2s ease;
    font-weight: 500;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

/* Card Enhancements */
.card {
    border-radius: 16px;
    transition: all 0.3s ease;
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(10px);
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.1) !important;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-radius: 16px 16px 0 0 !important;
}

/* Button Enhancements */
.btn {
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    padding: 10px 20px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.btn-outline-primary {
    border: 2px solid #667eea;
    color: #667eea;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: transparent;
}

/* Table Enhancements */
.table {
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.05);
}

.table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 600;
    padding: 15px;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
}

/* Form Enhancements */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    padding: 12px 15px;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Alert Enhancements */
.alert {
    border-radius: 12px;
    border: none;
    font-weight: 500;
}

/* Toast Enhancements */
.toast {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.toast-header {
    border-radius: 12px 12px 0 0;
    font-weight: 600;
}

/* Animation Classes */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.6s ease forwards;
}

/* Gradient Background */
.bg-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

/* Main Content */
main {
    flex: 1;
    padding-top: 2rem;
    padding-bottom: 2rem;
}

/* Responsive Utilities */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .card-body {
        padding: 1.5rem !important;
    }
}

/* Loading Animation */
.loading {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 2s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Status Badges */
.badge {
    border-radius: 8px;
    font-weight: 500;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Message Styles */
.message {
    background-color: #d40d0d;
    color: #fff;
    padding: 14px 24px;
    border-radius: 4px;
    box-shadow: 0 3px 5px -1px rgba(0,0,0,.2), 0 6px 10px 0 rgba(0,0,0,.14), 0 1px 18px 0 rgba(0,0,0,.12);
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-width: 250px;
    max-width: 500px;
    opacity: 0;
    transform: translateY(50px);
    transition: opacity 0.3s, transform 0.3s;
}

.message.show {
    opacity: 1;
    transform: translateY(0);
}

.message.hide {
    opacity: 0;
}

.message-content {
    flex-grow: 1;
}

.message-close {
    background: none;
    border: none;
    color: #fff;
    cursor: pointer;
    font-size: 18px;
    margin-left: 10px;
}

.message.success { background-color: #43a047; }
.message.error { background-color: #d32f2f; }
.message.warning { background-color: #ffa000; }
.message.info { background-color: #1976d2; }

/* Dark Theme - Code elements and RTSP URLs */
[data-bs-theme="dark"] code {
    background-color: #4a5568 !important;
    color: #e2e8f0 !important;
    border: 1px solid #718096 !important;
}

/* Dark Theme - Form containers and alert pages */
[data-bs-theme="dark"] .alert-container {
    background-color: #2d3748 !important;
    border-radius: 12px !important;
    padding: 2rem !important;
    border: 1px solid #4a5568 !important;
    color: #e2e8f0 !important;
}

[data-bs-theme="dark"] .auth-page {
    background: transparent !important;
    color: #e9ecef !important;
}

[data-bs-theme="dark"] .form-container {
    background-color: #2d3748 !important;
    border: 1px solid #4a5568 !important;
    color: #e2e8f0 !important;
    border-radius: 12px !important;
    padding: 2rem !important;
}

[data-bs-theme="dark"] .camera-container {
    background-color: #2d3748 !important;
    border-radius: 12px !important;
    padding: 2rem !important;
    border: 1px solid #4a5568 !important;
    color: #e2e8f0 !important;
}

[data-bs-theme="dark"] .alert-listing-container {
    background-color: #2d3748 !important;
    border-radius: 12px !important;
    padding: 2rem !important;
    border: 1px solid #4a5568 !important;
    color: #e2e8f0 !important;
}

/* Dark Theme - Alert info styling */
[data-bs-theme="dark"] .alert-info {
    background-color: #2b6cb0 !important;
    border-color: #3182ce !important;
    color: #e2e8f0 !important;
}

/* Dark Theme - Form labels and text readability */
[data-bs-theme="dark"] .form-group {
    color: #e2e8f0 !important;
}

[data-bs-theme="dark"] .form-label {
    color: #e2e8f0 !important;
    font-weight: 500;
}

[data-bs-theme="dark"] .form-text {
    color: #a0aec0 !important;
}

[data-bs-theme="dark"] small.text-muted {
    color: #a0aec0 !important;
}

/* Dark Theme - Profile username and email labels */
[data-bs-theme="dark"] small.text-muted {
    color: #cbd5e0 !important;
}

[data-bs-theme="dark"] .fw-semibold {
    color: #e2e8f0 !important;
}

/* Dark Theme - Button styling improvements */
[data-bs-theme="dark"] .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: none !important;
}

[data-bs-theme="dark"] .btn-outline-secondary {
    border-color: #718096 !important;
    color: #a0aec0 !important;
}

[data-bs-theme="dark"] .btn-outline-secondary:hover {
    background-color: #4a5568 !important;
    border-color: #a0aec0 !important;
    color: #e2e8f0 !important;
}

/* Dark Theme - Table styling */
[data-bs-theme="dark"] .table-light {
    background-color: #4a5568 !important;
    color: #e2e8f0 !important;
}

[data-bs-theme="dark"] .table thead th {
    background-color: #4a5568 !important;
    color: #e2e8f0 !important;
    border-color: #718096 !important;
}

[data-bs-theme="dark"] .table a {
    color: #63b3ed !important;
}

[data-bs-theme="dark"] .table a:hover {
    color: #90cdf4 !important;
}

/* Dark Theme - View details button styling */
[data-bs-theme="dark"] .btn-info {
    background-color: #3182ce !important;
    border-color: #3182ce !important;
    color: #ffffff !important;
}

[data-bs-theme="dark"] .btn-info:hover {
    background-color: #2c5282 !important;
    border-color: #2c5282 !important;
}

/* Dark Theme - Success button for rename */
[data-bs-theme="dark"] .btn-success {
    background-color: #38a169 !important;
    border-color: #38a169 !important;
}

[data-bs-theme="dark"] .btn-success:hover {
    background-color: #2f855a !important;
    border-color: #2f855a !important;
}
