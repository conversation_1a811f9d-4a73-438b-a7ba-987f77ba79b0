"""
ASGI config for web_server project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/howto/deployment/asgi/
"""

import os

# Import multiprocessing utilities for CUDA compatibility
from .mp_utils import setup_multiprocessing

from django.core.asgi import get_asgi_application
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack
import alerts.routing  # Ensure this import matches your app structure

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "web_server.settings")
print("web server ASGI application is being called")  # Debugging line

application = ProtocolTypeRouter({
    "http": get_asgi_application(),  # Handle HTTP requests
    "websocket": AuthMiddlewareStack(  # Handle WebSocket connections
        URLRouter(
            alerts.routing.websocket_urlpatterns  # Ensure this points to your WebSocket routing
        )
    ),
})

print("asgi")
