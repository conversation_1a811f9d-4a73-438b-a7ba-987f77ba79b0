{% extends 'base.html' %}

{% block content %}
<!-- <PERSON> ba<PERSON><PERSON><PERSON>k kartı -->
<div class="container-fluid d-flex justify-content-center align-items-center mb-4">
    <div class="name-card">
        <h1 class="person-title">{{ person.name }}</h1>
    </div>
</div>

<div class="alert-container">
    <h1 class="alert-title">
        <i class="bi bi-cloud-upload me-2"></i>Add Photos
    </h1>
    
    <!-- Basic Django Form Upload -->
    <div class="photo-upload-form">
        <form method="post" enctype="multipart/form-data">
            {% csrf_token %}
            <div class="mb-3">
                <label for="id_photos" class="form-label">Select Photos</label>
                {{ form.photos }}
            </div>
            <button type="submit" class="btn btn-primary">
                <i class="bi bi-upload me-2"></i>Upload Photos
            </button>
        </form>
    </div>

    <h1 class="alert-title">Existing Photos</h1>

    <!-- Photo Grid -->
    <div class="photo-gallery">
        <div class="row">
            {% for photo in person.photos.all %}
            <div class="col-md-3 col-sm-6 mb-4">
                <div class="photo-card card h-100">
                    <div class="photo-container">
                        <img src="{{ photo.photo.url }}" alt="{{ person.name }}" 
                             class="card-img-top photo-thumbnail">
                        {% if photo.is_primary %}
                        <span class="badge bg-primary position-absolute top-0 end-0 m-2">Primary</span>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        <p class="card-text">Added: {{ photo.created_at|date:"d M Y, H:i" }}</p>
                        <div class="d-flex justify-content-end mt-2">
                            <a href="{% url 'alerts:delete_photo' photo_id=photo.id %}" class="btn btn-danger btn-sm">
                                <i class="fas fa-trash"></i> Sil
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-12">
                <p class="paragraph">No photos uploaded yet.</p>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Tam ortalanmış kart stili */
    .name-card {
        background-color: white;
        border-radius: 12px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.08);
        padding: 15px 25px;
        text-align: center;
        display: inline-block;
        width: auto;
        min-width: 300px;
        transition: transform 0.2s, box-shadow 0.2s;
        border-left: 5px solid #5c7cfa;
    }
    
    .name-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 12px rgba(0,0,0,0.12);
    }
    
    .person-title {
        font-size: 2rem;
        font-weight: 600;
        color: #343a40;
        margin: 0;
        text-align: center;
    }

    /* Basic Photo Upload Form */
    .photo-upload-form {
        max-width: 600px;
        margin: 2rem auto;
        padding: 1.5rem;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    /* Basic form styling */
    .photo-upload-form .form-label {
        font-weight: 500;
        display: block;
        margin-bottom: 0.5rem;
    }
    
    .photo-upload-form input[type="file"] {
        display: block;
        width: 100%;
        padding: 0.5rem;
        margin-bottom: 1rem;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        background-color: #fff;
    }
    
    .photo-upload-form .btn-primary {
        background-color: #0d6efd;
        border-color: #0d6efd;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.2s;
    }
    
    .photo-upload-form .btn-primary:hover {
        background-color: #0b5ed7;
        border-color: #0a58ca;
    }

    /* Dark theme for name card and person title */
    [data-bs-theme="dark"] .name-card {
        background-color: #2d3748 !important;
        border: 1px solid #4a5568 !important;
        color: #e2e8f0 !important;
        border-left: 5px solid #63b3ed !important;
        box-shadow: 0 4px 8px rgba(0,0,0,0.3) !important;
    }
    
    [data-bs-theme="dark"] .name-card:hover {
        box-shadow: 0 6px 12px rgba(0,0,0,0.4) !important;
    }
    
    [data-bs-theme="dark"] .person-title {
        color: #e2e8f0 !important;
    }

    /* Dark theme for basic form */
    [data-bs-theme="dark"] .photo-upload-form {
        background-color: #2d3748 !important;
        border-color: #4a5568 !important;
    }
    
    [data-bs-theme="dark"] .photo-upload-form .form-label {
        color: #e2e8f0 !important;
    }
    
    [data-bs-theme="dark"] .photo-upload-form input[type="file"] {
        background-color: #1a202c !important;
        border-color: #4a5568 !important;
        color: #e2e8f0 !important;
    }
    
    [data-bs-theme="dark"] .photo-upload-form input[type="file"]::-webkit-file-upload-button {
        background-color: #4a5568 !important;
        border-color: #63b3ed !important;
        color: #e2e8f0 !important;
    }
    
    [data-bs-theme="dark"] .photo-upload-form .btn-primary {
        background-color: #4299e1 !important;
        border-color: #3182ce !important;
    }
    
    [data-bs-theme="dark"] .photo-upload-form .btn-primary:hover {
        background-color: #3182ce !important;
        border-color: #2b6cb0 !important;
    }

    /* Dark theme for photo gallery */
    [data-bs-theme="dark"] .photo-card {
        background-color: #2d3748 !important;
        border: 1px solid #4a5568 !important;
        color: #e2e8f0 !important;
    }
    
    [data-bs-theme="dark"] .photo-card .card-text {
        color: #a0aec0 !important;
    }

    /* Mevcut fotoğraf galerisi stilleri */
    .photo-gallery {
        margin-top: 20px;
    }
    
    .photo-thumbnail {
        height: 200px;
        object-fit: cover;
    }
    
    .photo-container {
        position: relative;
    }
    
    /* Silme butonu stilini özelleştir */
    .btn-danger.btn-sm {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }
</style>
{% endblock %}

{% block extra_js %}
<!-- No extra JavaScript needed for basic form -->
    
    // Click to browse files
    dragDropArea.addEventListener('click', () => fileInput.click());
    document.querySelector('.browse-link').addEventListener('click', () => fileInput.click());
    
    // Drag and drop events
    dragDropArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        dragDropArea.classList.add('drag-over');
    });
    
    dragDropArea.addEventListener('dragleave', (e) => {
        e.preventDefault();
        dragDropArea.classList.remove('drag-over');
    });
    
    dragDropArea.addEventListener('drop', (e) => {
        e.preventDefault();
        dragDropArea.classList.remove('drag-over');
        
        const files = Array.from(e.dataTransfer.files);
        handleFiles(files);
    });
    
    // File input change
    fileInput.addEventListener('change', (e) => {
        const files = Array.from(e.target.files);
        handleFiles(files);
    });
    
    // Handle selected files
    function handleFiles(files) {
        files.forEach(file => {
            if (file.type.startsWith('image/')) {
                selectedFiles.push(file);
                displayFile(file);
            }
        });
        
        updateUploadButton();
    }
    
    // Display file in preview list
    function displayFile(file) {
        const fileItem = document.createElement('div');
        fileItem.className = 'file-item';
        
        const reader = new FileReader();
        reader.onload = (e) => {
            fileItem.innerHTML = `
                <img src="${e.target.result}" class="file-preview" alt="Preview">
                <div class="file-info">
                    <div class="file-name">${file.name}</div>
                    <div class="file-size">${formatFileSize(file.size)}</div>
                </div>
                <button type="button" class="file-remove" onclick="removeFile('${file.name}')">
                    <i class="bi bi-x-lg"></i>
                </button>
            `;
        };
        reader.readAsDataURL(file);
        
        fileList.appendChild(fileItem);
    }
    
    // Remove file from selection
    window.removeFile = function(fileName) {
        selectedFiles = selectedFiles.filter(file => file.name !== fileName);
        
        // Remove from display
        const fileItems = fileList.querySelectorAll('.file-item');
        fileItems.forEach(item => {
            if (item.querySelector('.file-name').textContent === fileName) {
                item.remove();
            }
        });
        
        updateUploadButton();
    };
    
    // Clear all files
    document.getElementById('clear-files').addEventListener('click', () => {
        selectedFiles = [];
        fileList.innerHTML = '';
        fileInput.value = '';
        updateUploadButton();
    });
    
    // Update upload button
    function updateUploadButton() {
        if (selectedFiles.length > 0) {
            uploadActions.style.display = 'block';
            fileCount.textContent = selectedFiles.length;
        } else {
            uploadActions.style.display = 'none';
        }
    }
    
    // Format file size
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    // Form submission
    photoUploadForm.addEventListener('submit', (e) => {
        e.preventDefault();
        
        if (selectedFiles.length === 0) {
            showToast('Please select at least one photo to upload.', 'warning');
            return;
        }
        
        const formData = new FormData(photoUploadForm);
        
        // Remove default file input and add our selected files
        formData.delete('photos');
        selectedFiles.forEach(file => {
            formData.append('photos', file);
        });
        
        // Show loading state
        const submitBtn = photoUploadForm.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Uploading...';
        submitBtn.disabled = true;
        
        // Upload files
        fetch(window.location.href, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(`Successfully uploaded ${selectedFiles.length} photo(s)!`, 'success');
                selectedFiles = [];
                fileList.innerHTML = '';
                fileInput.value = '';
                updateUploadButton();
                
                // Reload page to show new photos
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showToast(data.error || 'Upload failed. Please try again.', 'error');
            }
        })
        .catch(error => {
            console.error('Upload error:', error);
            showToast('Upload failed. Please try again.', 'error');
        })
        .finally(() => {
            // Reset button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    });
});
</script>
{% endblock %}

