"""
WSGI config for web_server project.

It exposes the WSGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/howto/deployment/wsgi/
"""

import os

# Import multiprocessing utilities for CUDA compatibility
from .mp_utils import setup_multiprocessing

from django.core.wsgi import get_wsgi_application

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "web_server.settings")

application = get_wsgi_application()
