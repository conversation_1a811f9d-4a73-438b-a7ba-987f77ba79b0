# Face Recognition

This repository provides a face recognition solution. Follow the steps below to set up and start using the project.


## Setup Instructions

**Clone the Repository**

   ```bash
   git clone https://github.com/autodidactic-technologies/face_recognition.git
   cd face-recognition
   ```

## Conda Enviroment

`conda create face_recognition python=3.12`

`conda activate face_recognition`

**Install Requirements**

```bash
pip install -r requirements.txt
```

You can find the model files in the following URL:

https://drive.google.com/drive/u/1/folders/1mi5_fRV63dokrRsMguDrw-ThM4-DIsco

**Set Up Workspace Directory**

Place the ***work_space*** directory in the root of the project. Ensure it contains model files for the project to function correctly.

**Set Up ***media*** and ***alert_photos*** Folders**

- **demo_server**
  - **media**
    - alert_photos

## How to run the server
In the demo_server folder you can start in two ways.

If you don't need websocket functionality `python manage.py runserver` is enough to see the webpages.

To see the full functional websocket alarms use `uvicorn webserver.asgi:application`. (Uvicorn should have been install with `pip install uvicorn daphne`)

## Starting the Video Stream

For Linux: 
```bash
ffmpeg -f v4l2 -rtbufsize 300M -i /dev/video0 -f mjpeg -listen 1 http://127.0.0.1:8030/feed.mjpeg
```

For Windows: 
```bash
ffmpeg -f dshow -rtbufsize 300M -i video="your_camera_name" -f mjpeg -listen 1 http://127.0.0.1:8030/feed.mjpeg
```

For MacOs:
```
ffmpeg -f avfoundation -framerate pal -i "0:none"  -f mjpeg -listen 1 http://127.0.0.1:8030/feed.mjpeg
```
