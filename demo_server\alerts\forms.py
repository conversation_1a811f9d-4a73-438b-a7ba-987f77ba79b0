from django import forms
from .models import <PERSON><PERSON><PERSON><PERSON>, AlertPhoto

class Alert<PERSON>ersonForm(forms.ModelForm):
    class Meta:
        model = Alert<PERSON>erson
        fields = ['name']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'})
        }

class MultipleFileInput(forms.ClearableFileInput):
    allow_multiple_selected = True

class MultipleFileField(forms.FileField):
    def __init__(self, *args, **kwargs):
        kwargs.setdefault("widget", MultipleFileInput())
        super().__init__(*args, **kwargs)

    def clean(self, data, initial=None):
        single_file_clean = super().clean
        if isinstance(data, (list, tuple)):
            result = [single_file_clean(d, initial) for d in data]
        else:
            result = single_file_clean(data, initial)
        return result

class AlertPhotoForm(forms.Form):
    photos = MultipleFileField(required=True)

class RenameUnknownPersonForm(forms.Form):
    new_name = forms.CharField(
        max_length=100,
        label="New Name",
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter new name'})
    )

