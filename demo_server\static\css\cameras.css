.camera-container {
        max-width: 800px;
        margin: 3rem auto;
        padding: 2.5rem;
        background-color: white;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    }

    .camera-form {
        text-align: left;
    }

    .camera-form .form-label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: #333;
    }

    .camera-form .form-control,
    .camera-form .form-select {
        padding: 0.75rem;
        border-radius: 6px;
        border: 1px solid #e1e1e1;
        transition: border-color 0.2s, box-shadow 0.2s;
    }

    .camera-form .form-control:focus,
    .camera-form .form-select:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .model-selection-container {
        border-radius: 6px;
        background-color: #f9f9f9;
        padding: 0.5rem;
    }

    .form-text {
        font-size: 0.85rem;
        color: #6c757d;
        margin-top: 0.5rem;
    }

    .form-buttons {
        margin-top: 2rem;
        display: flex;
        justify-content: flex-start;
    }

    .delete-form {
    margin-top: 1.5rem;
}

    .btn-primary {
        background-color: #007bff;
        border: none;
        padding: 0.75rem 1.75rem;
        font-weight: 500;
        border-radius: 6px;
        transition: background-color 0.2s;
    }

    .btn-primary:hover {
        background-color: #0069d9;
    }

    .btn-outline-secondary {
        padding: 0.75rem 1.75rem;
        border-radius: 6px;
        font-weight: 500;
    }

    .status-badge {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 50px;
        font-weight: 500;
        font-size: 0.875rem;
    }

    .status-active {
        background-color: rgba(25, 135, 84, 0.15);
        color: #198754;
    }

    .status-inactive {
        background-color: rgba(108, 117, 125, 0.15);
        color: #6c757d;
    }

    .camera-listing-container {
        max-width: 1200px;
        margin: 3rem auto;
        padding: 2rem;
        background-color: white;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    }

    .table {
        width: 100%;
        margin-top: 1.5rem;
        border-collapse: separate;
        border-spacing: 0;
    }

    .table th {
        font-weight: 500;
        text-align: left;
        padding: 1rem;
        border-bottom: 2px solid #e1e1e1;
    }

    .table td {
        padding: 1rem;
        border-bottom: 1px solid #e1e1e1;
        vertical-align: middle;
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        margin-right: 0.25rem;
    }

/* Dark Theme Styles for Camera Pages */
[data-bs-theme="dark"] .camera-container {
    background-color: #2d3748 !important;
    border: 1px solid #4a5568 !important;
    color: #e2e8f0 !important;
}

[data-bs-theme="dark"] .camera-form .form-label {
    color: #e2e8f0 !important;
}

[data-bs-theme="dark"] .camera-form .form-control,
[data-bs-theme="dark"] .camera-form .form-select {
    background-color: #4a5568 !important;
    border-color: #718096 !important;
    color: #e2e8f0 !important;
}

[data-bs-theme="dark"] .camera-form .form-control:focus,
[data-bs-theme="dark"] .camera-form .form-select:focus {
    background-color: #4a5568 !important;
    border-color: #63b3ed !important;
    color: #e2e8f0 !important;
}

[data-bs-theme="dark"] .model-selection-container {
    background-color: #4a5568 !important;
    border: 1px solid #718096 !important;
}

[data-bs-theme="dark"] .form-text {
    color: #a0aec0 !important;
}

[data-bs-theme="dark"] .form-buttons {
    background-color: transparent !important;
}